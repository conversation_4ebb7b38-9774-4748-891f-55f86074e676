from typing import List


def removeDuplicates(nums: List[int]):
    s = set()
    return [i for i in nums if i not in s and (s.add(i) or 1)]


print(removeDuplicates([1,1,2]))
print(removeDuplicates([0,0,1,1,1,2,2,3,3,4]))

from hashlib import sha3_512

k = str(sha3_512(b"painful135.").hexdigest())
print(k[:8])
# 76c5d2d9

import math
def combinations_count(n, k):


    return math.factorial(n) // (math.factorial(k) * math.factorial(n - k))

pr