"""
法律问答Agent
负责分析用户问题，提取关键信息，并制定检索策略
"""

import re
import json
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum


class LegalDomain(Enum):
    """法律领域分类"""
    CONTRACT_LAW = "contract_law"
    TORT_LAW = "tort_law"
    CRIMINAL_LAW = "criminal_law"
    CONSTITUTIONAL_LAW = "constitutional_law"
    PROPERTY_LAW = "property_law"
    CORPORATE_LAW = "corporate_law"
    FAMILY_LAW = "family_law"
    EMPLOYMENT_LAW = "employment_law"
    INTELLECTUAL_PROPERTY = "intellectual_property"
    TAX_LAW = "tax_law"
    IMMIGRATION_LAW = "immigration_law"
    ENVIRONMENTAL_LAW = "environmental_law"
    UNKNOWN = "unknown"


class QueryType(Enum):
    """查询类型"""
    DEFINITION = "definition"  # 定义类问题
    PROCEDURE = "procedure"    # 程序类问题
    CASE_LAW = "case_law"     # 案例法问题
    STATUTE = "statute"       # 法条问题
    PRECEDENT = "precedent"   # 先例问题
    ADVICE = "advice"         # 建议类问题
    COMPARISON = "comparison" # 比较类问题
    UNKNOWN = "unknown"


@dataclass
class LegalEntity:
    """法律实体"""
    text: str
    entity_type: str
    confidence: float
    start_pos: int
    end_pos: int


@dataclass
class QueryAnalysis:
    """查询分析结果"""
    original_query: str
    cleaned_query: str
    domain: LegalDomain
    query_type: QueryType
    entities: List[LegalEntity]
    keywords: List[str]
    intent: str
    confidence: float
    search_strategy: Dict[str, Any]


class LegalAgent:
    """法律问答Agent"""
    
    def __init__(self):
        self.legal_keywords = self._load_legal_keywords()
        self.domain_patterns = self._load_domain_patterns()
        self.query_type_patterns = self._load_query_type_patterns()
    
    def _load_legal_keywords(self) -> Dict[str, List[str]]:
        """加载法律关键词库"""
        return {
            "contract_law": [
                "contract", "agreement", "breach", "consideration", "offer", "acceptance",
                "terms", "conditions", "warranty", "liability", "damages", "performance",
                "obligation", "covenant", "clause", "provision", "binding", "enforceable"
            ],
            "tort_law": [
                "negligence", "liability", "damages", "injury", "harm", "duty", "breach",
                "causation", "fault", "strict liability", "intentional tort", "defamation",
                "invasion of privacy", "assault", "battery", "false imprisonment"
            ],
            "criminal_law": [
                "crime", "criminal", "felony", "misdemeanor", "prosecution", "defendant",
                "guilty", "innocent", "sentence", "punishment", "bail", "arrest", "warrant",
                "evidence", "testimony", "jury", "verdict", "appeal", "conviction"
            ],
            "constitutional_law": [
                "constitution", "amendment", "rights", "freedom", "due process", "equal protection",
                "first amendment", "second amendment", "fourth amendment", "fifth amendment",
                "fourteenth amendment", "supreme court", "judicial review", "federalism"
            ],
            "property_law": [
                "property", "real estate", "ownership", "title", "deed", "mortgage", "lease",
                "landlord", "tenant", "easement", "zoning", "eminent domain", "foreclosure",
                "intellectual property", "patent", "trademark", "copyright"
            ],
            "corporate_law": [
                "corporation", "company", "business", "shareholder", "director", "officer",
                "merger", "acquisition", "securities", "stock", "dividend", "fiduciary duty",
                "corporate governance", "LLC", "partnership", "sole proprietorship"
            ],
            "family_law": [
                "marriage", "divorce", "custody", "child support", "alimony", "adoption",
                "domestic violence", "prenuptial", "separation", "paternity", "guardianship",
                "family court", "mediation", "visitation rights"
            ],
            "employment_law": [
                "employment", "employee", "employer", "workplace", "discrimination", "harassment",
                "wrongful termination", "wages", "overtime", "benefits", "workers compensation",
                "union", "collective bargaining", "FMLA", "ADA", "Title VII"
            ]
        }
    
    def _load_domain_patterns(self) -> Dict[LegalDomain, List[str]]:
        """加载领域识别模式"""
        return {
            LegalDomain.CONTRACT_LAW: [
                r"\b(contract|agreement|breach|consideration)\b",
                r"\b(offer|acceptance|terms|conditions)\b",
                r"\b(warranty|liability|damages|performance)\b"
            ],
            LegalDomain.TORT_LAW: [
                r"\b(negligence|tort|liability|injury)\b",
                r"\b(damages|harm|duty|causation)\b",
                r"\b(defamation|assault|battery)\b"
            ],
            LegalDomain.CRIMINAL_LAW: [
                r"\b(crime|criminal|felony|misdemeanor)\b",
                r"\b(prosecution|defendant|guilty|sentence)\b",
                r"\b(arrest|warrant|evidence|conviction)\b"
            ],
            LegalDomain.CONSTITUTIONAL_LAW: [
                r"\b(constitution|amendment|rights|freedom)\b",
                r"\b(due process|equal protection|supreme court)\b",
                r"\b(first amendment|second amendment|fourth amendment)\b"
            ],
            LegalDomain.PROPERTY_LAW: [
                r"\b(property|real estate|ownership|title)\b",
                r"\b(deed|mortgage|lease|landlord|tenant)\b",
                r"\b(patent|trademark|copyright)\b"
            ]
        }
    
    def _load_query_type_patterns(self) -> Dict[QueryType, List[str]]:
        """加载查询类型识别模式"""
        return {
            QueryType.DEFINITION: [
                r"\bwhat is\b", r"\bdefine\b", r"\bdefinition of\b",
                r"\bmeans?\b", r"\bmeaning of\b", r"\bexplain\b"
            ],
            QueryType.PROCEDURE: [
                r"\bhow to\b", r"\bprocess\b", r"\bprocedure\b",
                r"\bsteps\b", r"\bfile\b", r"\bapply\b"
            ],
            QueryType.CASE_LAW: [
                r"\bcase\b", r"\bcourt case\b", r"\bprecedent\b",
                r"\bruling\b", r"\bdecision\b", r"\bjudgment\b"
            ],
            QueryType.STATUTE: [
                r"\blaw\b", r"\bstatute\b", r"\bcode\b",
                r"\bregulation\b", r"\brule\b", r"\bact\b"
            ],
            QueryType.ADVICE: [
                r"\bshould I\b", r"\bwhat should\b", r"\badvice\b",
                r"\brecommend\b", r"\bsuggestion\b", r"\bhelp\b"
            ],
            QueryType.COMPARISON: [
                r"\bdifference\b", r"\bcompare\b", r"\bvs\b",
                r"\bversus\b", r"\bbetter\b", r"\bworse\b"
            ]
        }
    
    def analyze_query(self, query: str) -> QueryAnalysis:
        """分析用户查询"""
        # 清理查询
        cleaned_query = self._clean_query(query)
        
        # 识别法律领域
        domain = self._identify_domain(cleaned_query)
        
        # 识别查询类型
        query_type = self._identify_query_type(cleaned_query)
        
        # 提取实体
        entities = self._extract_entities(cleaned_query)
        
        # 提取关键词
        keywords = self._extract_keywords(cleaned_query, domain)
        
        # 分析意图
        intent = self._analyze_intent(cleaned_query, domain, query_type)
        
        # 计算置信度
        confidence = self._calculate_confidence(domain, query_type, entities, keywords)
        
        # 制定搜索策略
        search_strategy = self._create_search_strategy(domain, query_type, entities, keywords)
        
        return QueryAnalysis(
            original_query=query,
            cleaned_query=cleaned_query,
            domain=domain,
            query_type=query_type,
            entities=entities,
            keywords=keywords,
            intent=intent,
            confidence=confidence,
            search_strategy=search_strategy
        )
    
    def _clean_query(self, query: str) -> str:
        """清理查询文本"""
        # 转换为小写
        query = query.lower().strip()
        
        # 移除多余的空格
        query = re.sub(r'\s+', ' ', query)
        
        # 移除特殊字符（保留基本标点）
        query = re.sub(r'[^\w\s\-\.\?\!]', '', query)
        
        return query
    
    def _identify_domain(self, query: str) -> LegalDomain:
        """识别法律领域"""
        domain_scores = {}
        
        for domain, patterns in self.domain_patterns.items():
            score = 0
            for pattern in patterns:
                matches = len(re.findall(pattern, query, re.IGNORECASE))
                score += matches
            
            if score > 0:
                domain_scores[domain] = score
        
        if domain_scores:
            return max(domain_scores, key=domain_scores.get)
        
        return LegalDomain.UNKNOWN
    
    def _identify_query_type(self, query: str) -> QueryType:
        """识别查询类型"""
        type_scores = {}
        
        for query_type, patterns in self.query_type_patterns.items():
            score = 0
            for pattern in patterns:
                if re.search(pattern, query, re.IGNORECASE):
                    score += 1
            
            if score > 0:
                type_scores[query_type] = score
        
        if type_scores:
            return max(type_scores, key=type_scores.get)
        
        return QueryType.UNKNOWN
    
    def _extract_entities(self, query: str) -> List[LegalEntity]:
        """提取法律实体"""
        entities = []
        
        # 简单的实体识别模式
        entity_patterns = {
            "court": r"\b(supreme court|district court|appeals court|federal court|state court)\b",
            "case_name": r"\b([A-Z][a-z]+ v\.? [A-Z][a-z]+)\b",
            "statute": r"\b(USC|CFR|U\.S\.C\.)\s*\d+\b",
            "amendment": r"\b(first|second|third|fourth|fifth|sixth|seventh|eighth|ninth|tenth|eleventh|twelfth|thirteenth|fourteenth|fifteenth)\s+amendment\b",
            "legal_term": r"\b(plaintiff|defendant|appellant|appellee|petitioner|respondent)\b"
        }
        
        for entity_type, pattern in entity_patterns.items():
            matches = re.finditer(pattern, query, re.IGNORECASE)
            for match in matches:
                entities.append(LegalEntity(
                    text=match.group(),
                    entity_type=entity_type,
                    confidence=0.8,  # 简化的置信度
                    start_pos=match.start(),
                    end_pos=match.end()
                ))
        
        return entities
    
    def _extract_keywords(self, query: str, domain: LegalDomain) -> List[str]:
        """提取关键词"""
        keywords = []
        
        # 获取领域相关关键词
        domain_keywords = self.legal_keywords.get(domain.value, [])
        
        # 查找查询中的关键词
        words = query.split()
        for word in words:
            word = word.strip('.,!?')
            if word in domain_keywords:
                keywords.append(word)
        
        # 添加通用法律术语
        general_legal_terms = [
            "law", "legal", "court", "judge", "attorney", "lawyer", "case", "statute",
            "regulation", "rule", "right", "obligation", "liability", "penalty"
        ]
        
        for word in words:
            word = word.strip('.,!?')
            if word in general_legal_terms and word not in keywords:
                keywords.append(word)
        
        return list(set(keywords))  # 去重
    
    def _analyze_intent(self, query: str, domain: LegalDomain, query_type: QueryType) -> str:
        """分析用户意图"""
        intent_templates = {
            (LegalDomain.CONTRACT_LAW, QueryType.DEFINITION): "用户想了解合同法相关概念的定义",
            (LegalDomain.CONTRACT_LAW, QueryType.PROCEDURE): "用户想了解合同相关的法律程序",
            (LegalDomain.TORT_LAW, QueryType.CASE_LAW): "用户想查找侵权法相关的案例",
            (LegalDomain.CRIMINAL_LAW, QueryType.STATUTE): "用户想了解刑法条文",
            (LegalDomain.CONSTITUTIONAL_LAW, QueryType.PRECEDENT): "用户想了解宪法相关的先例"
        }
        
        key = (domain, query_type)
        if key in intent_templates:
            return intent_templates[key]
        
        return f"用户在{domain.value}领域进行{query_type.value}类型的查询"
    
    def _calculate_confidence(self, domain: LegalDomain, query_type: QueryType, 
                            entities: List[LegalEntity], keywords: List[str]) -> float:
        """计算分析置信度"""
        confidence = 0.0
        
        # 领域识别置信度
        if domain != LegalDomain.UNKNOWN:
            confidence += 0.3
        
        # 查询类型识别置信度
        if query_type != QueryType.UNKNOWN:
            confidence += 0.3
        
        # 实体识别置信度
        if entities:
            confidence += 0.2 * min(len(entities) / 3, 1.0)
        
        # 关键词识别置信度
        if keywords:
            confidence += 0.2 * min(len(keywords) / 5, 1.0)
        
        return min(confidence, 1.0)
    
    def _create_search_strategy(self, domain: LegalDomain, query_type: QueryType,
                              entities: List[LegalEntity], keywords: List[str]) -> Dict[str, Any]:
        """制定搜索策略"""
        strategy = {
            "primary_domain": domain.value,
            "query_type": query_type.value,
            "search_fields": [],
            "boost_terms": [],
            "filters": {},
            "ranking_factors": {}
        }
        
        # 根据查询类型调整搜索策略
        if query_type == QueryType.DEFINITION:
            strategy["search_fields"] = ["title", "summary", "definitions"]
            strategy["boost_terms"] = ["definition", "means", "refers to"]
        elif query_type == QueryType.CASE_LAW:
            strategy["search_fields"] = ["case_name", "court", "decision", "facts"]
            strategy["filters"]["document_type"] = "case"
        elif query_type == QueryType.STATUTE:
            strategy["search_fields"] = ["statute_number", "title", "text"]
            strategy["filters"]["document_type"] = "statute"
        elif query_type == QueryType.PROCEDURE:
            strategy["search_fields"] = ["procedure", "steps", "process"]
            strategy["boost_terms"] = ["how to", "procedure", "process"]
        
        # 添加实体信息
        if entities:
            strategy["entities"] = [
                {"text": e.text, "type": e.entity_type} for e in entities
            ]
        
        # 添加关键词
        strategy["keywords"] = keywords
        
        # 设置排序因子
        strategy["ranking_factors"] = {
            "relevance": 0.4,
            "authority": 0.3,
            "recency": 0.2,
            "completeness": 0.1
        }
        
        return strategy


if __name__ == "__main__":
    # 测试法律Agent
    agent = LegalAgent()
    
    test_queries = [
        "What is a breach of contract?",
        "How to file for divorce in California?",
        "What are the elements of negligence in tort law?",
        "Can you explain the Fourth Amendment?",
        "What is the difference between felony and misdemeanor?",
        "How does copyright law protect intellectual property?",
        "What are the requirements for a valid contract?",
        "What is the statute of limitations for personal injury cases?"
    ]
    
    print("=== 法律Agent测试 ===\n")
    
    for i, query in enumerate(test_queries, 1):
        print(f"查询 {i}: {query}")
        analysis = agent.analyze_query(query)
        
        print(f"  领域: {analysis.domain.value}")
        print(f"  类型: {analysis.query_type.value}")
        print(f"  关键词: {', '.join(analysis.keywords)}")
        print(f"  实体: {[e.text for e in analysis.entities]}")
        print(f"  意图: {analysis.intent}")
        print(f"  置信度: {analysis.confidence:.2f}")
        print(f"  搜索策略: {json.dumps(analysis.search_strategy, indent=2)}")
        print("-" * 50)
