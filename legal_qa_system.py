"""
美国法律问答系统
整合Agent分析、一致性哈希和RAG检索的完整系统
"""

import json
import time
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict

from consistent_hash import ConsistentHash, DistributedStorage
from legal_agent import LegalAgent, QueryAnalysis
from rag_retrieval import VectorStore, RAGRetriever, SearchResult
from legal_knowledge import LegalKnowledgeBase


@dataclass
class QAResponse:
    """问答响应"""
    query: str
    answer: str
    confidence: float
    sources: List[Dict[str, Any]]
    analysis: Dict[str, Any]
    processing_time: float
    retrieval_strategy: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return asdict(self)


class LegalQASystem:
    """美国法律问答系统"""
    
    def __init__(self, nodes: List[str] = None):
        """
        初始化法律问答系统
        
        Args:
            nodes: 分布式节点列表，用于一致性哈希
        """
        print("正在初始化法律问答系统...")
        
        # 初始化组件
        self.agent = LegalAgent()
        self.knowledge_base = LegalKnowledgeBase()
        
        # 设置默认节点
        if nodes is None:
            nodes = ["legal_node_1", "legal_node_2", "legal_node_3", "legal_node_4"]
        
        # 初始化一致性哈希和分布式存储
        self.consistent_hash = ConsistentHash(nodes)
        self.distributed_storage = DistributedStorage(nodes)
        
        # 初始化向量存储和检索器
        self.vector_store = VectorStore(embedding_dim=200)
        
        # 加载知识库到向量存储
        print("正在加载知识库...")
        documents = self.knowledge_base.get_all_documents()
        self.vector_store.add_documents(documents)
        
        # 初始化RAG检索器
        self.retriever = RAGRetriever(self.vector_store)
        
        # 分布式存储文档
        print("正在分布式存储文档...")
        self._distribute_documents(documents)
        
        # 系统统计
        self.query_count = 0
        self.total_processing_time = 0.0
        
        print("法律问答系统初始化完成!")
    
    def _distribute_documents(self, documents) -> None:
        """将文档分布式存储到各个节点"""
        for doc in documents:
            # 使用文档ID作为key进行一致性哈希分布
            self.distributed_storage.put(
                key=doc.id,
                value=doc.to_dict(),
                replica_count=2  # 每个文档存储2个副本
            )
    
    def ask(self, query: str) -> QAResponse:
        """
        处理法律问答查询
        
        Args:
            query: 用户查询
            
        Returns:
            QAResponse: 问答响应
        """
        start_time = time.time()
        
        try:
            # 1. Agent分析查询
            print(f"\n=== 处理查询: {query} ===")
            print("1. 正在分析查询...")
            analysis = self.agent.analyze_query(query)
            
            print(f"   - 识别领域: {analysis.domain.value}")
            print(f"   - 查询类型: {analysis.query_type.value}")
            print(f"   - 关键词: {', '.join(analysis.keywords)}")
            print(f"   - 置信度: {analysis.confidence:.2f}")
            
            # 2. 使用一致性哈希确定查询路由
            print("2. 正在确定查询路由...")
            query_node = self.consistent_hash.get_node(query)
            print(f"   - 路由到节点: {query_node}")
            
            # 3. RAG检索相关文档
            print("3. 正在检索相关文档...")
            search_results = self.retriever.retrieve(
                query=analysis.cleaned_query,
                search_strategy=analysis.search_strategy,
                top_k=5
            )
            
            print(f"   - 找到 {len(search_results)} 个相关文档")
            for i, result in enumerate(search_results[:3], 1):
                print(f"     {i}. {result.document.title} (相似度: {result.score:.3f})")
            
            # 4. 生成答案
            print("4. 正在生成答案...")
            answer = self._generate_answer(analysis, search_results)
            
            # 5. 计算整体置信度
            overall_confidence = self._calculate_overall_confidence(analysis, search_results)
            
            # 6. 准备响应
            processing_time = time.time() - start_time
            
            # 更新统计
            self.query_count += 1
            self.total_processing_time += processing_time
            
            response = QAResponse(
                query=query,
                answer=answer,
                confidence=overall_confidence,
                sources=[self._format_source(result) for result in search_results],
                analysis={
                    'domain': analysis.domain.value,
                    'query_type': analysis.query_type.value,
                    'keywords': analysis.keywords,
                    'entities': [{'text': e.text, 'type': e.entity_type} for e in analysis.entities],
                    'intent': analysis.intent,
                    'agent_confidence': analysis.confidence
                },
                processing_time=processing_time,
                retrieval_strategy=analysis.search_strategy
            )
            
            print(f"5. 处理完成 (耗时: {processing_time:.2f}秒)")
            return response
            
        except Exception as e:
            print(f"处理查询时发生错误: {e}")
            processing_time = time.time() - start_time
            
            return QAResponse(
                query=query,
                answer=f"抱歉，处理您的查询时发生错误: {str(e)}",
                confidence=0.0,
                sources=[],
                analysis={},
                processing_time=processing_time,
                retrieval_strategy={}
            )
    
    def _generate_answer(self, analysis: QueryAnalysis, search_results: List[SearchResult]) -> str:
        """生成答案"""
        if not search_results:
            return "抱歉，我没有找到与您的查询相关的法律信息。请尝试重新表述您的问题或提供更多详细信息。"
        
        # 根据查询类型生成不同风格的答案
        if analysis.query_type.value == "definition":
            return self._generate_definition_answer(analysis, search_results)
        elif analysis.query_type.value == "procedure":
            return self._generate_procedure_answer(analysis, search_results)
        elif analysis.query_type.value == "case_law":
            return self._generate_case_law_answer(analysis, search_results)
        elif analysis.query_type.value == "statute":
            return self._generate_statute_answer(analysis, search_results)
        else:
            return self._generate_general_answer(analysis, search_results)
    
    def _generate_definition_answer(self, analysis: QueryAnalysis, search_results: List[SearchResult]) -> str:
        """生成定义类答案"""
        best_result = search_results[0]
        
        answer_parts = []
        answer_parts.append(f"根据美国{analysis.domain.value.replace('_', ' ')}，")
        
        # 从最相关的文档中提取定义信息
        content = best_result.document.content
        sentences = content.split('.')
        
        # 查找包含关键词的句子作为定义
        for sentence in sentences:
            sentence = sentence.strip()
            if any(keyword.lower() in sentence.lower() for keyword in analysis.keywords):
                answer_parts.append(sentence + ".")
                break
        
        # 添加补充信息
        if len(search_results) > 1:
            answer_parts.append(f"\n\n补充信息来自 {search_results[1].document.title}:")
            if search_results[1].highlights:
                answer_parts.append(search_results[1].highlights[0])
        
        return " ".join(answer_parts)
    
    def _generate_procedure_answer(self, analysis: QueryAnalysis, search_results: List[SearchResult]) -> str:
        """生成程序类答案"""
        answer_parts = []
        answer_parts.append(f"关于{analysis.intent}，以下是相关的法律程序和要求：\n")
        
        for i, result in enumerate(search_results[:2], 1):
            answer_parts.append(f"{i}. 根据 {result.document.title}:")
            if result.highlights:
                answer_parts.append(f"   {result.highlights[0]}\n")
            else:
                # 提取程序相关的句子
                content = result.document.content
                sentences = content.split('.')
                for sentence in sentences:
                    if any(word in sentence.lower() for word in ['step', 'procedure', 'process', 'require', 'must']):
                        answer_parts.append(f"   {sentence.strip()}.\n")
                        break
        
        return "\n".join(answer_parts)
    
    def _generate_case_law_answer(self, analysis: QueryAnalysis, search_results: List[SearchResult]) -> str:
        """生成案例法答案"""
        answer_parts = []
        answer_parts.append(f"关于{analysis.intent}，以下是相关的案例法和先例：\n")
        
        for i, result in enumerate(search_results[:3], 1):
            doc = result.document
            answer_parts.append(f"{i}. {doc.title}")
            
            # 添加案例信息
            if doc.metadata.get('document_type') == 'case':
                jurisdiction = doc.metadata.get('jurisdiction', '未知管辖区')
                authority = doc.metadata.get('authority', '未知法院')
                answer_parts.append(f"   管辖区: {jurisdiction}, 法院级别: {authority}")
            
            if result.highlights:
                answer_parts.append(f"   关键内容: {result.highlights[0]}")
            
            answer_parts.append("")
        
        return "\n".join(answer_parts)
    
    def _generate_statute_answer(self, analysis: QueryAnalysis, search_results: List[SearchResult]) -> str:
        """生成法条答案"""
        answer_parts = []
        answer_parts.append(f"关于{analysis.intent}，以下是相关的法律条文：\n")
        
        for i, result in enumerate(search_results[:2], 1):
            doc = result.document
            answer_parts.append(f"{i}. {doc.title}")
            
            # 添加法条信息
            source = doc.metadata.get('source', '未知来源')
            authority = doc.metadata.get('authority', '未知权威')
            answer_parts.append(f"   来源: {source}, 权威级别: {authority}")
            
            if result.highlights:
                answer_parts.append(f"   条文内容: {result.highlights[0]}")
            
            answer_parts.append("")
        
        return "\n".join(answer_parts)
    
    def _generate_general_answer(self, analysis: QueryAnalysis, search_results: List[SearchResult]) -> str:
        """生成通用答案"""
        best_result = search_results[0]
        
        answer_parts = []
        answer_parts.append(f"根据美国法律，关于您的查询：\n")
        answer_parts.append(f"主要参考: {best_result.document.title}")
        
        if best_result.highlights:
            answer_parts.append(f"\n{best_result.highlights[0]}")
        
        # 添加其他相关信息
        if len(search_results) > 1:
            answer_parts.append(f"\n相关信息还可以参考:")
            for result in search_results[1:3]:
                answer_parts.append(f"- {result.document.title}")
        
        return "\n".join(answer_parts)
    
    def _calculate_overall_confidence(self, analysis: QueryAnalysis, search_results: List[SearchResult]) -> float:
        """计算整体置信度"""
        if not search_results:
            return 0.0
        
        # 组合多个因素计算置信度
        agent_confidence = analysis.confidence
        retrieval_confidence = sum(result.score for result in search_results) / len(search_results)
        
        # 根据文档权威性调整置信度
        authority_boost = 0.0
        for result in search_results:
            authority = result.document.metadata.get('authority', 'unknown')
            if authority == 'supreme_court':
                authority_boost += 0.1
            elif authority == 'federal':
                authority_boost += 0.05
        
        overall_confidence = (agent_confidence * 0.4 + retrieval_confidence * 0.5 + authority_boost * 0.1)
        return min(overall_confidence, 1.0)
    
    def _format_source(self, result: SearchResult) -> Dict[str, Any]:
        """格式化来源信息"""
        doc = result.document
        return {
            'title': doc.title,
            'document_type': doc.metadata.get('document_type', 'unknown'),
            'domain': doc.metadata.get('domain', 'unknown'),
            'authority': doc.metadata.get('authority', 'unknown'),
            'jurisdiction': doc.metadata.get('jurisdiction', 'unknown'),
            'source': doc.metadata.get('source', 'unknown'),
            'relevance_score': result.score,
            'explanation': result.explanation,
            'highlights': result.highlights[:2]  # 最多2个高亮
        }
    
    def get_system_stats(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        kb_stats = self.knowledge_base.get_knowledge_base_stats()
        hash_info = self.consistent_hash.get_ring_info()
        storage_info = self.distributed_storage.get_storage_info()
        
        avg_processing_time = (self.total_processing_time / self.query_count 
                             if self.query_count > 0 else 0.0)
        
        return {
            'knowledge_base': kb_stats,
            'consistent_hash': hash_info,
            'distributed_storage': storage_info,
            'query_statistics': {
                'total_queries': self.query_count,
                'total_processing_time': self.total_processing_time,
                'average_processing_time': avg_processing_time
            }
        }
    
    def add_node(self, node: str) -> None:
        """添加新的分布式节点"""
        self.consistent_hash.add_node(node)
        self.distributed_storage.add_node(node)
        print(f"已添加新节点: {node}")
    
    def remove_node(self, node: str) -> None:
        """移除分布式节点"""
        self.consistent_hash.remove_node(node)
        self.distributed_storage.remove_node(node)
        print(f"已移除节点: {node}")


def main():
    """主函数 - 演示系统功能"""
    print("=== 美国法律问答系统演示 ===\n")
    
    # 初始化系统
    qa_system = LegalQASystem()
    
    # 测试查询
    test_queries = [
        "What is a breach of contract?",
        "What are the elements of negligence in tort law?",
        "How does the Fourth Amendment protect against unreasonable searches?",
        "What is the difference between felony and misdemeanor?",
        "What are the requirements for a valid marriage?",
        "What types of property ownership exist in real estate law?",
        "What is strict liability in tort law?",
        "How does the First Amendment protect freedom of speech?"
    ]
    
    print("开始处理测试查询...\n")
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n{'='*60}")
        print(f"测试查询 {i}/{len(test_queries)}")
        print(f"{'='*60}")
        
        response = qa_system.ask(query)
        
        print(f"\n问题: {response.query}")
        print(f"\n答案: {response.answer}")
        print(f"\n置信度: {response.confidence:.2f}")
        print(f"处理时间: {response.processing_time:.2f}秒")
        print(f"\n主要来源:")
        for source in response.sources[:2]:
            print(f"  - {source['title']} (相关度: {source['relevance_score']:.3f})")
        
        if i < len(test_queries):
            print("\n" + "-"*40 + " 继续下一个查询 " + "-"*40)
    
    # 显示系统统计
    print(f"\n{'='*60}")
    print("系统统计信息")
    print(f"{'='*60}")
    
    stats = qa_system.get_system_stats()
    print(f"\n知识库统计:")
    print(f"  总文档数: {stats['knowledge_base']['total_documents']}")
    print(f"  领域分布: {stats['knowledge_base']['domains']}")
    
    print(f"\n查询统计:")
    print(f"  总查询数: {stats['query_statistics']['total_queries']}")
    print(f"  平均处理时间: {stats['query_statistics']['average_processing_time']:.2f}秒")
    
    print(f"\n分布式存储:")
    print(f"  物理节点数: {stats['consistent_hash']['physical_nodes']}")
    print(f"  虚拟节点数: {stats['consistent_hash']['total_virtual_nodes']}")
    print(f"  存储分布: {stats['distributed_storage']['storage_distribution']}")


if __name__ == "__main__":
    main()
