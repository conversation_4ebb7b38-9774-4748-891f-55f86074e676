
# Definition for a Node.
class Node(object):
    def __init__(self, val, children=None):
        self.val = val
        self.children = children if children is not None else []

    def add_children(self, *children):
        [self.children.append(child) for child in children]

    def insert_child(self, child):
        self.children.insert(0, child)


class Solution(object):
    def preorder(self, root):
        """
        :type root: Node
        :rtype: List[int]
        """
        if not root:
            return []
        if not root.children:
            return [root.val]
        result = [root.val]
        for child in root.children:
            result += self.preorder(child)
        return result


from leetcode.t2 import s

l = []

root = Node(0)
# for i in s.split('\n'):
#     if i:
#         print(i)
#         if i[1] == ' ':
#             root.add_children(Node(i.split(' ')[0]))

print(root.children)

ss = s.split('\n')[1:-1]

def f(root, children):
    for i, v in enumerate(ss):
        if v[1] == ' ':
            new_node = Node(v.split(' ')[0])
            root.add_children(new_node)
            # f(new_node, ss[i:])
        else:
            new_node = Node(v.split(' ')[0])
            # f(new_node, ss[i:])
    return root

# root = f(root, ss)
# print(root)
# print([i.val for i in root.children])

from treelib import Tree

# print(make_list_tree())

t = ''
def f1():
    root = Node(0)
    t = ''
    for i in ss:
        if i[1] == ' ':
            t += '    |' * 0 + '-' * 2 + f'{i}' + '\\' + '\n'

        else:
            p = i.split('.')
            t += '    |' * len(p) + '-' * 2 + f'{i}' + '\n'
    print(t)

# print(f1())

n_l = [i.split('.') for i in ss]
print(n_l)

def f2(n_l):
    d = {}
    root = Node('0')
    b = 9999999
    for i, v in enumerate(reversed(n_l)):
        k = len(v)
        print(i, v)

        if k > b:
            # d = {}
            print(108, d)
            for j in range(k - b):
                d.pop(k - j, None)
            print(109, d)
        b = k

        if k > 1:
            if k not in d:
                d[k] = [Node(v[-1])]
            else:
                d[k] = [Node(v[-1])] + d[k]
            print(95, d)
            if k + 1 in d:
                d[k][-1].add_children(*d[k + 1])
            print(97, d[k][-1].children)

        else:
            new_node = Node(v[-1])
            if k + 1 in d:
                new_node.add_children(*d[k + 1])
            print(104, new_node.children, d)
            root.insert_child(new_node)


    return root
print(s)

# print(f2(n_l))
root = f2(n_l)

def preorder(root):
    """
    :type root: Node
    :rtype: List[int]
    """
    if root is None:
        return []

    stack, output = [root, ], []
    while stack:
        root = stack.pop()
        # print(121, root.val, root.children)
        output.append(root.val)
        stack.extend(root.children[::-1])

    return output

print(root.val, root.children[0].val, root.children[0].children)
print(preorder(root))
print(['0'] + [i[-1] for i in n_l])
# print(preorder(root))
    # for i, v in enumerate(reversed(ss)):
    #     p = v.split('.')
    #     if len(p) > 1:
    #         new_node = Node(p[-1])

# print(len(ss[1].split('.')))

# class C:
#     def __init__(self, s):
#         self.tree = ''
#         self.s = s
#
#     def generate_tree(self, n=0):
#         if self.pathname.is_file():
#             self.tree += '    |' * n + '-' * 2 + self.pathname.name + '\n'
#         elif self.pathname.is_dir():
#             self.tree += '    |' * n + '-' * 2 + \
#                          str(self.pathname.relative_to(self.pathname.parent)) + '\\' + '\n'
#
#             for cp in self.pathname.iterdir():
#                 self.pathname = Path(cp)
#                 self.generate_tree(n + 1)