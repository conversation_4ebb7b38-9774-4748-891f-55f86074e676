from typing import Optional


class ListNode:
    def __init__(self, val=0, next=None):
        self.val = val
        self.next = next

    def __repr__(self):
        return f'<{self.val, self.next}>'


def addTwoNumbers(l1: Optional[ListNode], l2: Optional[ListNode]) -> Optional[ListNode]:

    cur = dummy = ListNode()
    carry = 0 # 进位

    while l1 or l2 or carry:
        if l1:
            carry += l1.val
            l1 = l1.next
        if l2:
            carry += l2.val
            l2 = l2.next

        cur.next = ListNode(carry % 10)
        cur = cur.next

        carry = carry // 10

    return dummy.next

    #     val = l1.val + l2.val + carry
    #     print(f'{val=}')
    #     carry = val > 9
    #     l1.val = val % 10
    #
    #     print(f'{l1=}, {l2=}, {carry=}')
    #     if carry:
    #         if l1.next and not l2.next:
    #             l2.next = ListNode(1)
    #             continue
    #         elif l2.next and not l1.next:
    #             l1.next = ListNode(1)
    #             continue
    #
    #     l1 = l1.next
    #     l2 = l2.next
    #
    #     print(f'{node=}')
    #
    # return node

    # if l1:
    #     l1.next = l2
    #     return node
    # else:
    #     l2.next = l1
    #     return node
        # if l1:
        #     l1.val = l1.val if not l2 else l1.val + l2.val
        #     node = l1
        #     l1 = l1.next
        # else:
        #     l2.val = l2.val if not l1 else l1.val + l2.val
        #     node = l2
        #     l2 = l2.next


l1 = ListNode(5, ListNode(6, ListNode(9)))
l2 = ListNode(9, ListNode(5))

# l1 = ListNode(3, ListNode(4, ListNode(2)))
# l2 = ListNode(9, ListNode(8, ListNode(6, ListNode(5))))

print(addTwoNumbers(l1, l2))


def addTwoNumbers2(l1: Optional[ListNode], l2: Optional[ListNode]) -> Optional[ListNode]:
    cur = dummy = ListNode()  # 哨兵节点
    carry = 0  # 进位
    while l1 or l2 or carry:  # 有一个不是空节点，或者还有进位，就继续迭代
        if l1:
            carry += l1.val  # 节点值和进位加在一起
            l1 = l1.next  # 下一个节点
        if l2:
            carry += l2.val  # 节点值和进位加在一起
            l2 = l2.next  # 下一个节点
        cur.next = ListNode(carry % 10)  # 每个节点保存一个数位
        carry //= 10  # 新的进位
        cur = cur.next  # 下一个节点
    return dummy.next  # 哨兵节点的下一个节点就是头节点

l1 = ListNode(5, ListNode(6, ListNode(9)))
l2 = ListNode(9, ListNode(5))

print(addTwoNumbers2(l1, l2))

# l = ListNode()
# d = l
# for i in range(10):
#     l.next = ListNode(i)
#     l = l.next
#
# print(f'{d=}')
# while d:
#     print(f'{d=}')
#     d = d.next