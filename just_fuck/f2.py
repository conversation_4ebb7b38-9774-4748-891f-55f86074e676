from collections import defaultdict
from typing import List


class Solution:
    def lengthOfLongestSubstring(self, s: str) -> int:
        max_l = 0
        for i, v in enumerate(s):
            for j, w in enumerate(s[i:]):
                # print(f'{i=}, {j=}, {w=}, {s[i:j+i]=}, {s[i:]=}, {len(s[i:])=}, {j == len(s[i:]) - 1}')
                if w in s[i:j+i]:
                    max_l = max(j, max_l)
                    break
                elif j == len(s[i:]) - 1:
                    max_l = max(j + 1, max_l)
                    # print(max_l, j+1, 'aaa')

        return max_l

# print(Solution().lengthOfLongestSubstring("abcdefg"))
# print(Solution().lengthOfLongestSubstring(" "))
# print(Solution().lengthOfLongestSubstring("aab"))
# print(Solution().lengthOfLongestSubstring("ohomm"))


class Solution:

    def lengthOfLongestSubstring(self, s: str) -> int:
        max_l = 0
        d = set()
        for i, v in enumerate(s):
            for j, w in enumerate(s[i:]):
                if w in d:
                    max_l = max(j, max_l)
                    d.clear()
                    break
                elif j == len(s[i:]) - 1:
                    max_l = max(j + 1, max_l)
                    d.clear()
                d.add(w)

        return max_l

# print(Solution().lengthOfLongestSubstring("abcdefg"))
# print(Solution().lengthOfLongestSubstring(" "))
# print(Solution().lengthOfLongestSubstring("aab"))
# print(Solution().lengthOfLongestSubstring("ohomm"))
# print(Solution().lengthOfLongestSubstring("pwwkew"))

print('////////////')

class Solution:
    def lengthOfLongestSubstring(self, s: str) -> int:
        max_l = 0
        c = 0
        d = set()
        ti = 0
        for i, v in enumerate(s):
            print(f'{v=}, {d=}, {v in d}')
            while v in d:
                ti += 1
                print(f'{ti=}')
                d.discard(s[c])
                c += 1

            d.add(v)
            max_l = max(max_l, i - c + 1)

        return max_l


print(Solution().lengthOfLongestSubstring("pwwkew"))

# class Solution:
#     def lengthOfLongestSubstring(self, s: str) -> int:
#         dic, res, i = {}, 0, -1
#         for j in range(len(s)):
#             if s[j] in dic:
#                 i = max(dic[s[j]], i) # 更新左指针 i
#             dic[s[j]] = j # 哈希表记录
#             res = max(res, j - i) # 更新结果
#         return res
#
# print(Solution().lengthOfLongestSubstring("pwwkew"))

def findMedianSortedArrays(nums1: List[int], nums2: List[int]) -> float:
    nums1.extend(nums2) or nums1.sort()
    i = len(nums1) // 2
    print(i)
    print(nums1)
    print(i - 1, i)
    return nums1[i] if len(nums1) % 2 else sum(nums1[i - 1: i + 1]) / 2

print(findMedianSortedArrays([1, 3], [2, 4]))