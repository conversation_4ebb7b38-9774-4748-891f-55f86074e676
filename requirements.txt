# 美国法律问答系统依赖包

# 核心科学计算库
numpy>=1.21.0
scipy>=1.7.0

# 机器学习和自然语言处理
scikit-learn>=1.0.0
nltk>=3.6.0

# 数据处理
pandas>=1.3.0

# 可选：更高级的NLP库（如果需要）
# transformers>=4.0.0
# torch>=1.9.0
# sentence-transformers>=2.0.0

# 可选：向量数据库（如果需要持久化存储）
# faiss-cpu>=1.7.0
# chromadb>=0.3.0

# 可选：Web框架（如果需要API服务）
# fastapi>=0.68.0
# uvicorn>=0.15.0
# pydantic>=1.8.0

# 开发和测试工具
pytest>=6.0.0
pytest-cov>=2.12.0

# 代码质量工具
flake8>=3.9.0
black>=21.0.0
isort>=5.9.0

# 文档生成
# sphinx>=4.0.0
# sphinx-rtd-theme>=0.5.0

# 性能分析
# memory-profiler>=0.58.0
# line-profiler>=3.3.0

# 日志和监控
# loguru>=0.5.0
# prometheus-client>=0.11.0

# 配置管理
# python-dotenv>=0.19.0
# pyyaml>=5.4.0

# 数据序列化
# pickle5>=0.0.11  # Python < 3.8
# joblib>=1.0.0

# 时间处理
# python-dateutil>=2.8.0

# 正则表达式增强
# regex>=2021.8.0

# 并发处理
# concurrent-futures>=3.1.1  # Python < 3.2

# 网络请求（如果需要外部API）
# requests>=2.25.0
# httpx>=0.24.0

# 缓存
# redis>=3.5.0  # 如果使用Redis缓存
# memcached>=1.59  # 如果使用Memcached

# 数据库（如果需要持久化）
# sqlalchemy>=1.4.0
# sqlite3  # 内置模块

# 加密和安全
# cryptography>=3.4.0
# bcrypt>=3.2.0

# 环境和部署
# docker>=5.0.0
# kubernetes>=18.0.0

# 监控和日志
# structlog>=21.1.0
# sentry-sdk>=1.3.0
