"""
美国法律问答系统简化演示
不依赖外部库的基本功能展示
"""

import hashlib
import re
import time
import json
from typing import List, Dict, Any, Optional


class SimpleConsistentHash:
    """简化的一致性哈希实现"""
    
    def __init__(self, nodes: List[str]):
        self.nodes = nodes
        self.ring = {}
        self._build_ring()
    
    def _hash(self, key: str) -> int:
        """计算哈希值"""
        return int(hashlib.md5(key.encode()).hexdigest(), 16) % (2**32)
    
    def _build_ring(self):
        """构建哈希环"""
        for node in self.nodes:
            for i in range(100):  # 每个节点100个虚拟节点
                virtual_key = f"{node}:{i}"
                hash_val = self._hash(virtual_key)
                self.ring[hash_val] = node
    
    def get_node(self, key: str) -> str:
        """获取key对应的节点"""
        if not self.ring:
            return self.nodes[0] if self.nodes else "default"
        
        key_hash = self._hash(key)
        sorted_hashes = sorted(self.ring.keys())
        
        for hash_val in sorted_hashes:
            if hash_val >= key_hash:
                return self.ring[hash_val]
        
        return self.ring[sorted_hashes[0]]


class SimpleLegalAgent:
    """简化的法律Agent"""
    
    def __init__(self):
        self.legal_domains = {
            'contract_law': ['contract', 'agreement', 'breach', 'consideration', 'offer', 'acceptance'],
            'tort_law': ['negligence', 'liability', 'damages', 'injury', 'duty', 'causation'],
            'criminal_law': ['crime', 'criminal', 'felony', 'prosecution', 'guilty', 'sentence'],
            'constitutional_law': ['constitution', 'amendment', 'rights', 'freedom', 'supreme court'],
            'property_law': ['property', 'ownership', 'real estate', 'title', 'deed']
        }
        
        self.query_types = {
            'definition': ['what is', 'define', 'definition', 'meaning'],
            'procedure': ['how to', 'process', 'steps', 'procedure'],
            'case_law': ['case', 'court case', 'ruling', 'decision'],
            'statute': ['law', 'statute', 'code', 'regulation']
        }
    
    def analyze_query(self, query: str) -> Dict[str, Any]:
        """分析查询"""
        query_lower = query.lower()
        
        # 识别领域
        domain_scores = {}
        for domain, keywords in self.legal_domains.items():
            score = sum(1 for keyword in keywords if keyword in query_lower)
            if score > 0:
                domain_scores[domain] = score
        
        best_domain = max(domain_scores, key=domain_scores.get) if domain_scores else 'unknown'
        
        # 识别查询类型
        type_scores = {}
        for qtype, patterns in self.query_types.items():
            score = sum(1 for pattern in patterns if pattern in query_lower)
            if score > 0:
                type_scores[qtype] = score
        
        best_type = max(type_scores, key=type_scores.get) if type_scores else 'unknown'
        
        # 提取关键词
        words = re.findall(r'\b\w+\b', query_lower)
        keywords = []
        for domain_keywords in self.legal_domains.values():
            keywords.extend([word for word in words if word in domain_keywords])
        
        return {
            'domain': best_domain,
            'query_type': best_type,
            'keywords': list(set(keywords)),
            'confidence': min(len(keywords) / 3.0, 1.0)
        }


class SimpleLegalKnowledge:
    """简化的法律知识库"""
    
    def __init__(self):
        self.documents = {
            'contract_001': {
                'title': 'Elements of a Valid Contract',
                'content': 'A valid contract requires four essential elements: offer, acceptance, consideration, and mutual assent. An offer is a clear proposal to enter into an agreement. Acceptance must mirror the terms of the offer exactly. Consideration is something of value exchanged between parties.',
                'domain': 'contract_law',
                'type': 'statute'
            },
            'contract_002': {
                'title': 'Breach of Contract',
                'content': 'A breach of contract occurs when one party fails to perform obligations specified in the contract. Material breach excuses the non-breaching party from further performance. Remedies include compensatory damages, consequential damages, and specific performance.',
                'domain': 'contract_law',
                'type': 'case'
            },
            'tort_001': {
                'title': 'Elements of Negligence',
                'content': 'Negligence requires four elements: duty, breach of duty, causation, and damages. The defendant must owe a legal duty of care to the plaintiff. The defendant must breach that duty by failing to meet the standard of care. The breach must cause the plaintiff harm.',
                'domain': 'tort_law',
                'type': 'case'
            },
            'criminal_001': {
                'title': 'Elements of a Crime',
                'content': 'Most crimes consist of actus reus (guilty act) and mens rea (guilty mind). Actus reus requires a voluntary act or omission. Mens rea includes purposely, knowingly, recklessly, and negligently. The prosecution must prove all elements beyond reasonable doubt.',
                'domain': 'criminal_law',
                'type': 'statute'
            },
            'constitutional_001': {
                'title': 'First Amendment Rights',
                'content': 'The First Amendment protects freedom of speech, religion, press, assembly, and petition. Content-based restrictions receive strict scrutiny. The government cannot engage in prior restraint except in extraordinary circumstances.',
                'domain': 'constitutional_law',
                'type': 'statute'
            }
        }
    
    def search_documents(self, query: str, domain: str = None) -> List[Dict[str, Any]]:
        """搜索文档"""
        query_words = set(re.findall(r'\b\w+\b', query.lower()))
        results = []
        
        for doc_id, doc in self.documents.items():
            if domain and doc['domain'] != domain:
                continue
            
            # 计算相似度
            doc_words = set(re.findall(r'\b\w+\b', doc['content'].lower()))
            common_words = query_words.intersection(doc_words)
            similarity = len(common_words) / len(query_words.union(doc_words)) if query_words.union(doc_words) else 0
            
            if similarity > 0:
                results.append({
                    'id': doc_id,
                    'title': doc['title'],
                    'content': doc['content'],
                    'domain': doc['domain'],
                    'type': doc['type'],
                    'similarity': similarity
                })
        
        # 按相似度排序
        results.sort(key=lambda x: x['similarity'], reverse=True)
        return results


class SimpleLegalQA:
    """简化的法律问答系统"""
    
    def __init__(self):
        self.consistent_hash = SimpleConsistentHash(['node1', 'node2', 'node3'])
        self.agent = SimpleLegalAgent()
        self.knowledge = SimpleLegalKnowledge()
        self.query_count = 0
    
    def ask(self, query: str) -> Dict[str, Any]:
        """处理问答查询"""
        start_time = time.time()
        self.query_count += 1
        
        print(f"\n=== 处理查询 {self.query_count}: {query} ===")
        
        # 1. Agent分析
        print("1. 正在分析查询...")
        analysis = self.agent.analyze_query(query)
        print(f"   - 识别领域: {analysis['domain']}")
        print(f"   - 查询类型: {analysis['query_type']}")
        print(f"   - 关键词: {', '.join(analysis['keywords'])}")
        print(f"   - 置信度: {analysis['confidence']:.2f}")
        
        # 2. 一致性哈希路由
        print("2. 正在确定查询路由...")
        target_node = self.consistent_hash.get_node(query)
        print(f"   - 路由到节点: {target_node}")
        
        # 3. 检索相关文档
        print("3. 正在检索相关文档...")
        domain_filter = analysis['domain'] if analysis['domain'] != 'unknown' else None
        documents = self.knowledge.search_documents(query, domain_filter)
        print(f"   - 找到 {len(documents)} 个相关文档")
        
        for i, doc in enumerate(documents[:3], 1):
            print(f"     {i}. {doc['title']} (相似度: {doc['similarity']:.3f})")
        
        # 4. 生成答案
        print("4. 正在生成答案...")
        answer = self._generate_answer(query, analysis, documents)
        
        processing_time = time.time() - start_time
        print(f"5. 处理完成 (耗时: {processing_time:.2f}秒)")
        
        return {
            'query': query,
            'answer': answer,
            'analysis': analysis,
            'documents': documents[:3],
            'target_node': target_node,
            'processing_time': processing_time
        }
    
    def _generate_answer(self, query: str, analysis: Dict[str, Any], documents: List[Dict[str, Any]]) -> str:
        """生成答案"""
        if not documents:
            return "抱歉，我没有找到与您的查询相关的法律信息。请尝试重新表述您的问题。"
        
        best_doc = documents[0]
        
        if analysis['query_type'] == 'definition':
            answer = f"根据美国{analysis['domain'].replace('_', ' ')}，{best_doc['content'][:200]}..."
        elif analysis['query_type'] == 'procedure':
            answer = f"关于您询问的程序，根据{best_doc['title']}：{best_doc['content'][:200]}..."
        else:
            answer = f"根据{best_doc['title']}，{best_doc['content'][:200]}..."
        
        if len(documents) > 1:
            answer += f"\n\n相关信息还可以参考：{documents[1]['title']}"
        
        return answer
    
    def get_stats(self) -> Dict[str, Any]:
        """获取系统统计"""
        return {
            'total_queries': self.query_count,
            'total_documents': len(self.knowledge.documents),
            'nodes': self.consistent_hash.nodes,
            'domains': list(self.agent.legal_domains.keys())
        }


def main():
    """主演示函数"""
    print("=== 美国法律问答系统简化演示 ===\n")
    
    # 初始化系统
    qa_system = SimpleLegalQA()
    
    # 测试查询
    test_queries = [
        "What is a breach of contract?",
        "What are the elements of negligence?",
        "How does criminal law define a crime?",
        "What is the First Amendment?",
        "What are the requirements for a valid contract?"
    ]
    
    print("开始处理测试查询...\n")
    
    for query in test_queries:
        response = qa_system.ask(query)
        
        print(f"\n问题: {response['query']}")
        print(f"\n答案: {response['answer']}")
        print(f"\n主要来源: {response['documents'][0]['title'] if response['documents'] else '无'}")
        print(f"处理时间: {response['processing_time']:.2f}秒")
        print(f"路由节点: {response['target_node']}")
        
        print("\n" + "="*60)
    
    # 显示系统统计
    stats = qa_system.get_stats()
    print(f"\n系统统计:")
    print(f"  总查询数: {stats['total_queries']}")
    print(f"  文档数量: {stats['total_documents']}")
    print(f"  分布式节点: {', '.join(stats['nodes'])}")
    print(f"  支持领域: {', '.join(stats['domains'])}")
    
    print(f"\n演示完成! 🎉")


if __name__ == "__main__":
    main()
