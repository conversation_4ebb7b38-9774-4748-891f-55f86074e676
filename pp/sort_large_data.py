import time


def cost(f):

   def inner(*args, **kwargs):
       t = time.time()
       r = f(*args, **kwargs)
       print(f'{f.__name__} cost: {time.time() - t}')
       return r

   return inner


@cost
def external_merge_sort(input_file, output_file, chunk_size=1024*1024*10):
    from pathlib import Path
    pp = Path('temp')
    pp.mkdir(exist_ok=True)

    # 1. 分割成小文件并排序
    def split_and_sort():
        temp_files = []
        with open(input_file, 'r') as f:
            chunk = []
            for line in f:
                chunk.append(int(line))
                # print(int(line))
                if len(chunk) >= chunk_size:
                    chunk.sort()
                    temp_file = f'temp/temp_{len(temp_files)}.txt'
                    with open(temp_file, 'w') as tf:
                        print(f'{chunk=}')
                        for num in chunk:
                            tf.write(f'{num}\n')
                    temp_files.append(temp_file)
                    chunk = []
        return temp_files

    # 2. 合并排序后的文件
    def merge_files(files, output):
        with open(output, 'w') as out:
            # 为每个临时文件创建迭代器
            file_iters = []
            for fname in files:
                f = open(fname, 'r')
                file_iters.append(iter(f))
            
            # 使用堆来合并
            import heapq
            heap = []
            for i, it in enumerate(file_iters):
                try:
                    val = next(it)
                    heapq.heappush(heap, (int(val), i))
                except StopIteration:
                    pass

            while heap:
                val, i = heapq.heappop(heap)
                out.write(f'{val}\n')
                try:
                    next_val = next(file_iters[i])
                    heapq.heappush(heap, (int(next_val), i))

                except StopIteration:
                    pass

    # 执行排序
    temp_files = split_and_sort()
    print(temp_files)
    merge_files(temp_files, output_file)

path1 = 's1.txt'
path2 = 's2.txt'
out_path = 'ss.txt'
# external_merge_sort(path1, out_path)
# external_merge_sort(path2, out_path)
external_merge_sort('s3.txt', out_path)

# with open('s3.txt', 'w', encoding='utf-8') as f:
#     f.write('\n'.join(f'{i}' for i in range(100000000, 0, -1)))

# with open('ss.txt', 'w', encoding='utf-8') as f:
#     f.write('')