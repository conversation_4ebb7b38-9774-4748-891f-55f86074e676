import mmap
import os
import time


def cost(f):

   def inner(*args, **kwargs):
       t = time.time()
       r = f(*args, **kwargs)
       print(f'{f.__name__} cost: {time.time() - t}')
       return r

   return inner

@cost
def mmap_sort(filename):
    # 获取文件大小
    size = os.path.getsize(filename)

    with open(filename, 'r+b') as f:
        # 创建内存映射
        mm = mmap.mmap(f.fileno(), 0)

        # 读取数据到列表中
        data = []
        start = 0
        while start < size:
            mm.seek(start)
            line = mm.readline()
            if not line: break
            data.append(int(line))
            start = mm.tell()

        # 排序
        data.sort()

        # 写回文件
        mm.seek(0)
        for num in data:
            mm.write(f'{num}\n'.encode())

        mm.flush()
        mm.close()



# with open('ss.txt', 'w', encoding='utf-8') as f:
#     f.write('')



mmap_sort('s3.txt')