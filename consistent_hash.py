"""
一致性哈希实现
用于分布式系统中的负载均衡和数据分片
"""

import hashlib
import bisect
from typing import List, Dict, Any, Optional


class ConsistentHash:
    """一致性哈希环实现"""
    
    def __init__(self, nodes: List[str] = None, replicas: int = 150):
        """
        初始化一致性哈希环
        
        Args:
            nodes: 初始节点列表
            replicas: 每个物理节点对应的虚拟节点数量
        """
        self.replicas = replicas
        self.ring = {}  # 哈希环: {hash_value: node_name}
        self.sorted_keys = []  # 排序的哈希值列表
        
        if nodes:
            for node in nodes:
                self.add_node(node)
    
    def _hash(self, key: str) -> int:
        """计算字符串的哈希值"""
        return int(hashlib.md5(key.encode('utf-8')).hexdigest(), 16)
    
    def add_node(self, node: str) -> None:
        """添加节点到哈希环"""
        for i in range(self.replicas):
            virtual_key = f"{node}:{i}"
            hash_value = self._hash(virtual_key)
            self.ring[hash_value] = node
            bisect.insort(self.sorted_keys, hash_value)
    
    def remove_node(self, node: str) -> None:
        """从哈希环中移除节点"""
        for i in range(self.replicas):
            virtual_key = f"{node}:{i}"
            hash_value = self._hash(virtual_key)
            if hash_value in self.ring:
                del self.ring[hash_value]
                self.sorted_keys.remove(hash_value)
    
    def get_node(self, key: str) -> Optional[str]:
        """获取key对应的节点"""
        if not self.ring:
            return None
        
        hash_value = self._hash(key)
        
        # 使用二分查找找到第一个大于等于hash_value的位置
        idx = bisect.bisect_right(self.sorted_keys, hash_value)
        
        # 如果超出范围，则回到环的开始
        if idx == len(self.sorted_keys):
            idx = 0
        
        return self.ring[self.sorted_keys[idx]]
    
    def get_nodes(self, key: str, count: int = 1) -> List[str]:
        """获取key对应的多个节点（用于副本）"""
        if not self.ring or count <= 0:
            return []
        
        hash_value = self._hash(key)
        idx = bisect.bisect_right(self.sorted_keys, hash_value)
        
        nodes = []
        seen_nodes = set()
        
        for _ in range(len(self.sorted_keys)):
            if idx >= len(self.sorted_keys):
                idx = 0
            
            node = self.ring[self.sorted_keys[idx]]
            if node not in seen_nodes:
                nodes.append(node)
                seen_nodes.add(node)
                
                if len(nodes) >= count:
                    break
            
            idx += 1
        
        return nodes
    
    def get_ring_info(self) -> Dict[str, Any]:
        """获取哈希环的信息"""
        node_count = {}
        for node in self.ring.values():
            node_count[node] = node_count.get(node, 0) + 1
        
        return {
            'total_virtual_nodes': len(self.ring),
            'physical_nodes': len(set(self.ring.values())),
            'node_distribution': node_count,
            'ring_size': len(self.sorted_keys)
        }


class DistributedStorage:
    """基于一致性哈希的分布式存储示例"""
    
    def __init__(self, nodes: List[str], replicas: int = 150):
        self.hash_ring = ConsistentHash(nodes, replicas)
        self.storage = {node: {} for node in nodes}
    
    def put(self, key: str, value: Any, replica_count: int = 2) -> bool:
        """存储数据到分布式节点"""
        try:
            nodes = self.hash_ring.get_nodes(key, replica_count)
            for node in nodes:
                if node in self.storage:
                    self.storage[node][key] = value
            return True
        except Exception as e:
            print(f"Error storing key {key}: {e}")
            return False
    
    def get(self, key: str) -> Optional[Any]:
        """从分布式节点获取数据"""
        node = self.hash_ring.get_node(key)
        if node and node in self.storage:
            return self.storage[node].get(key)
        return None
    
    def delete(self, key: str, replica_count: int = 2) -> bool:
        """从分布式节点删除数据"""
        try:
            nodes = self.hash_ring.get_nodes(key, replica_count)
            for node in nodes:
                if node in self.storage and key in self.storage[node]:
                    del self.storage[node][key]
            return True
        except Exception as e:
            print(f"Error deleting key {key}: {e}")
            return False
    
    def add_node(self, node: str) -> None:
        """添加新节点并重新平衡数据"""
        self.hash_ring.add_node(node)
        self.storage[node] = {}
        # 在实际应用中，这里需要实现数据迁移逻辑
    
    def remove_node(self, node: str) -> None:
        """移除节点并重新平衡数据"""
        if node in self.storage:
            # 在实际应用中，这里需要实现数据迁移逻辑
            data_to_migrate = self.storage[node].copy()
            del self.storage[node]
            self.hash_ring.remove_node(node)
            
            # 重新分布数据
            for key, value in data_to_migrate.items():
                self.put(key, value)
    
    def get_storage_info(self) -> Dict[str, Any]:
        """获取存储信息"""
        info = self.hash_ring.get_ring_info()
        info['storage_distribution'] = {
            node: len(data) for node, data in self.storage.items()
        }
        return info


if __name__ == "__main__":
    # 测试一致性哈希
    print("=== 一致性哈希测试 ===")
    
    # 创建哈希环
    nodes = ["server1", "server2", "server3"]
    ch = ConsistentHash(nodes)
    
    # 测试数据分布
    test_keys = [f"legal_doc_{i}" for i in range(20)]
    distribution = {}
    
    for key in test_keys:
        node = ch.get_node(key)
        distribution[node] = distribution.get(node, 0) + 1
    
    print("数据分布:")
    for node, count in distribution.items():
        print(f"  {node}: {count} 个文档")
    
    print(f"\n哈希环信息: {ch.get_ring_info()}")
    
    # 测试分布式存储
    print("\n=== 分布式存储测试 ===")
    storage = DistributedStorage(nodes)
    
    # 存储一些法律文档
    legal_docs = {
        "contract_law_001": "合同法基础知识...",
        "tort_law_002": "侵权法案例分析...",
        "criminal_law_003": "刑法条文解释...",
        "constitutional_law_004": "宪法修正案...",
        "property_law_005": "财产法原理..."
    }
    
    for doc_id, content in legal_docs.items():
        storage.put(doc_id, content)
        print(f"存储文档: {doc_id}")
    
    # 检索文档
    print("\n检索测试:")
    for doc_id in ["contract_law_001", "tort_law_002"]:
        content = storage.get(doc_id)
        print(f"{doc_id}: {content[:20]}..." if content else f"{doc_id}: 未找到")
    
    print(f"\n存储信息: {storage.get_storage_info()}")
