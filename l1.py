class ListNode:
    def __init__(self, val=0, next=None):
        self.val = val
        self.next = next

class Solution:

    @classmethod
    def addTwoNumbers(self, l1: ListNode, l2: ListNode) -> ListNode:
        dummy = p = ListNode(None)
        s = 0

        while l1 or l2 or s != 0:
            s += (l1.val if l1 else 0) + (l2.val if l2 else 0)
            p.next = ListNode(s % 10)
            p = p.next
            if l1: l1 = l1.next
            if l2: l2 = l2.next
            s = s // 10

        return dummy.next


def addTwoNumbers(l1: ListNode, l2: ListNode) -> ListNode:
    dummy = p = ListNode(None)
    s = 0

    while l1 or l2 or s != 0:
        s += (l1.val if l1 else 0) + (l2.val if l2 else 0)
        p.next = ListNode(s % 10)
        p = p.next
        if l1: l1 = l1.next
        if l2: l2 = l2.next
        s = s // 10

    return dummy.next

l1 = ListNode(2, ListNode(4, ListNode(3)))
l2 = ListNode(5, ListNode(6, ListNode(4)))

l = addTwoNumbers(l1, l2)
print(l)
s = ''
s += str(l.val)
while l.next:
    l = l.next
    s += str(l.val)
print(s)
