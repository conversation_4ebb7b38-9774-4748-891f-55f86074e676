"""
法律问答系统测试文件
包含单元测试和集成测试
"""

import unittest
import time
import json
from typing import List, Dict, Any

from consistent_hash import ConsistentHash, DistributedStorage
from legal_agent import LegalAgent, LegalDomain, QueryType
from rag_retrieval import VectorStore, RAGRetriever, Document
from legal_knowledge import LegalKnowledgeBase
from legal_qa_system import LegalQASystem
from utils import Timer, Cache, TextUtils, HashUtils, ValidationUtils


class TestConsistentHash(unittest.TestCase):
    """一致性哈希测试"""
    
    def setUp(self):
        self.nodes = ["node1", "node2", "node3"]
        self.ch = ConsistentHash(self.nodes)
    
    def test_node_addition(self):
        """测试节点添加"""
        initial_nodes = len(set(self.ch.ring.values()))
        self.ch.add_node("node4")
        final_nodes = len(set(self.ch.ring.values()))
        self.assertEqual(final_nodes, initial_nodes + 1)
    
    def test_node_removal(self):
        """测试节点移除"""
        initial_nodes = len(set(self.ch.ring.values()))
        self.ch.remove_node("node1")
        final_nodes = len(set(self.ch.ring.values()))
        self.assertEqual(final_nodes, initial_nodes - 1)
    
    def test_key_distribution(self):
        """测试键分布"""
        keys = [f"key_{i}" for i in range(100)]
        distribution = {}
        
        for key in keys:
            node = self.ch.get_node(key)
            distribution[node] = distribution.get(node, 0) + 1
        
        # 检查所有节点都有分配
        self.assertEqual(len(distribution), len(self.nodes))
        
        # 检查分布相对均匀（允许一定偏差）
        avg_load = 100 / len(self.nodes)
        for node, count in distribution.items():
            self.assertGreater(count, avg_load * 0.5)  # 至少50%的平均负载
            self.assertLess(count, avg_load * 2.0)     # 最多200%的平均负载
    
    def test_consistency(self):
        """测试一致性"""
        key = "test_key"
        node1 = self.ch.get_node(key)
        
        # 添加节点后，大部分键应该保持在原节点
        self.ch.add_node("node4")
        node2 = self.ch.get_node(key)
        
        # 虽然可能改变，但应该是确定性的
        self.assertIsNotNone(node2)


class TestLegalAgent(unittest.TestCase):
    """法律Agent测试"""
    
    def setUp(self):
        self.agent = LegalAgent()
    
    def test_domain_identification(self):
        """测试领域识别"""
        test_cases = [
            ("What is a breach of contract?", LegalDomain.CONTRACT_LAW),
            ("Elements of negligence in tort law", LegalDomain.TORT_LAW),
            ("Fourth Amendment search and seizure", LegalDomain.CONSTITUTIONAL_LAW),
            ("Criminal law actus reus", LegalDomain.CRIMINAL_LAW),
            ("Property ownership types", LegalDomain.PROPERTY_LAW)
        ]
        
        for query, expected_domain in test_cases:
            analysis = self.agent.analyze_query(query)
            self.assertEqual(analysis.domain, expected_domain)
    
    def test_query_type_identification(self):
        """测试查询类型识别"""
        test_cases = [
            ("What is negligence?", QueryType.DEFINITION),
            ("How to file for divorce?", QueryType.PROCEDURE),
            ("Palsgraf v. Long Island Railroad case", QueryType.CASE_LAW),
            ("Title VII statute requirements", QueryType.STATUTE)
        ]
        
        for query, expected_type in test_cases:
            analysis = self.agent.analyze_query(query)
            self.assertEqual(analysis.query_type, expected_type)
    
    def test_keyword_extraction(self):
        """测试关键词提取"""
        query = "What are the elements of a valid contract?"
        analysis = self.agent.analyze_query(query)
        
        # 应该包含合同法相关关键词
        expected_keywords = ["contract", "elements"]
        for keyword in expected_keywords:
            self.assertIn(keyword, analysis.keywords)
    
    def test_confidence_calculation(self):
        """测试置信度计算"""
        # 明确的法律查询应该有较高置信度
        clear_query = "What is breach of contract in contract law?"
        analysis = self.agent.analyze_query(clear_query)
        self.assertGreater(analysis.confidence, 0.5)
        
        # 模糊查询应该有较低置信度
        vague_query = "What is this thing?"
        analysis = self.agent.analyze_query(vague_query)
        self.assertLess(analysis.confidence, 0.5)


class TestVectorStore(unittest.TestCase):
    """向量存储测试"""
    
    def setUp(self):
        self.vector_store = VectorStore(embedding_dim=50)
        self.test_documents = [
            Document(
                id="doc1",
                title="Contract Law Basics",
                content="A contract is a legally binding agreement between parties.",
                metadata={"domain": "contract_law", "type": "statute"}
            ),
            Document(
                id="doc2",
                title="Tort Law Negligence",
                content="Negligence requires duty, breach, causation, and damages.",
                metadata={"domain": "tort_law", "type": "case"}
            )
        ]
    
    def test_document_addition(self):
        """测试文档添加"""
        self.vector_store.add_documents(self.test_documents)
        self.assertEqual(len(self.vector_store.documents), 2)
        self.assertTrue(self.vector_store.is_fitted)
    
    def test_vector_search(self):
        """测试向量搜索"""
        self.vector_store.add_documents(self.test_documents)
        
        # 搜索合同相关内容
        results = self.vector_store.search("contract agreement", top_k=2)
        self.assertGreater(len(results), 0)
        
        # 第一个结果应该是合同法文档
        best_result = results[0]
        self.assertEqual(best_result[0], "doc1")
    
    def test_filtered_search(self):
        """测试过滤搜索"""
        self.vector_store.add_documents(self.test_documents)
        
        # 只搜索侵权法文档
        filters = {"domain": "tort_law"}
        results = self.vector_store.search("negligence", filters=filters)
        
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0][0], "doc2")


class TestRAGRetriever(unittest.TestCase):
    """RAG检索器测试"""
    
    def setUp(self):
        self.vector_store = VectorStore(embedding_dim=50)
        self.test_documents = [
            Document(
                id="doc1",
                title="Contract Formation",
                content="Contract formation requires offer, acceptance, consideration, and mutual assent. These are the essential elements.",
                metadata={"domain": "contract_law", "document_type": "statute"}
            ),
            Document(
                id="doc2",
                title="Negligence Elements",
                content="To prove negligence, plaintiff must show duty, breach of duty, causation, and damages. All four elements are required.",
                metadata={"domain": "tort_law", "document_type": "case"}
            )
        ]
        self.vector_store.add_documents(self.test_documents)
        self.retriever = RAGRetriever(self.vector_store)
    
    def test_basic_retrieval(self):
        """测试基本检索"""
        search_strategy = {
            "keywords": ["contract", "elements"],
            "boost_terms": ["formation"],
            "filters": {}
        }
        
        results = self.retriever.retrieve("contract elements", search_strategy, top_k=2)
        self.assertGreater(len(results), 0)
        
        # 检查结果结构
        result = results[0]
        self.assertIsNotNone(result.document)
        self.assertIsInstance(result.score, float)
        self.assertIsInstance(result.highlights, list)
        self.assertIsInstance(result.explanation, str)
    
    def test_highlight_generation(self):
        """测试高亮生成"""
        search_strategy = {
            "keywords": ["negligence", "duty"],
            "boost_terms": [],
            "filters": {}
        }
        
        results = self.retriever.retrieve("negligence duty", search_strategy, top_k=1)
        self.assertGreater(len(results), 0)
        
        result = results[0]
        self.assertGreater(len(result.highlights), 0)


class TestLegalKnowledgeBase(unittest.TestCase):
    """法律知识库测试"""
    
    def setUp(self):
        self.kb = LegalKnowledgeBase()
    
    def test_knowledge_base_loading(self):
        """测试知识库加载"""
        documents = self.kb.get_all_documents()
        self.assertGreater(len(documents), 0)
        
        # 检查文档结构
        doc = documents[0]
        self.assertIsNotNone(doc.id)
        self.assertIsNotNone(doc.title)
        self.assertIsNotNone(doc.content)
        self.assertIsInstance(doc.metadata, dict)
    
    def test_domain_filtering(self):
        """测试领域过滤"""
        contract_docs = self.kb.get_documents_by_domain("contract_law")
        self.assertGreater(len(contract_docs), 0)
        
        # 所有文档都应该是合同法领域
        for doc in contract_docs:
            self.assertEqual(doc.metadata.get("domain"), "contract_law")
    
    def test_type_filtering(self):
        """测试类型过滤"""
        case_docs = self.kb.get_documents_by_type("case")
        statute_docs = self.kb.get_documents_by_type("statute")
        
        # 应该有不同类型的文档
        self.assertGreater(len(case_docs), 0)
        self.assertGreater(len(statute_docs), 0)
    
    def test_tag_search(self):
        """测试标签搜索"""
        negligence_docs = self.kb.search_by_tags(["negligence"])
        self.assertGreater(len(negligence_docs), 0)
        
        # 检查返回的文档确实包含相关标签
        for doc in negligence_docs:
            tags = doc.metadata.get("tags", [])
            self.assertTrue(any("negligence" in tag for tag in tags))


class TestLegalQASystem(unittest.TestCase):
    """法律问答系统集成测试"""
    
    def setUp(self):
        # 使用较小的配置进行测试
        self.qa_system = LegalQASystem(nodes=["test_node_1", "test_node_2"])
    
    def test_system_initialization(self):
        """测试系统初始化"""
        self.assertIsNotNone(self.qa_system.agent)
        self.assertIsNotNone(self.qa_system.knowledge_base)
        self.assertIsNotNone(self.qa_system.vector_store)
        self.assertIsNotNone(self.qa_system.retriever)
        self.assertIsNotNone(self.qa_system.consistent_hash)
    
    def test_query_processing(self):
        """测试查询处理"""
        query = "What is a breach of contract?"
        response = self.qa_system.ask(query)
        
        # 检查响应结构
        self.assertEqual(response.query, query)
        self.assertIsInstance(response.answer, str)
        self.assertGreater(len(response.answer), 0)
        self.assertIsInstance(response.confidence, float)
        self.assertGreaterEqual(response.confidence, 0.0)
        self.assertLessEqual(response.confidence, 1.0)
        self.assertIsInstance(response.sources, list)
        self.assertIsInstance(response.analysis, dict)
        self.assertGreater(response.processing_time, 0.0)
    
    def test_multiple_queries(self):
        """测试多个查询"""
        queries = [
            "What is negligence?",
            "How does the Fourth Amendment work?",
            "What are the elements of a contract?"
        ]
        
        for query in queries:
            response = self.qa_system.ask(query)
            self.assertIsNotNone(response)
            self.assertGreater(len(response.answer), 0)
    
    def test_system_stats(self):
        """测试系统统计"""
        # 处理一些查询
        self.qa_system.ask("What is contract law?")
        self.qa_system.ask("What is tort law?")
        
        stats = self.qa_system.get_system_stats()
        
        # 检查统计结构
        self.assertIn("knowledge_base", stats)
        self.assertIn("consistent_hash", stats)
        self.assertIn("query_statistics", stats)
        
        # 检查查询统计
        query_stats = stats["query_statistics"]
        self.assertEqual(query_stats["total_queries"], 2)
        self.assertGreater(query_stats["total_processing_time"], 0.0)


class TestUtils(unittest.TestCase):
    """工具函数测试"""
    
    def test_timer(self):
        """测试计时器"""
        with Timer() as timer:
            time.sleep(0.01)  # 睡眠10毫秒
        
        self.assertGreater(timer.elapsed(), 0.005)  # 至少5毫秒
        self.assertLess(timer.elapsed(), 0.1)       # 少于100毫秒
    
    def test_cache(self):
        """测试缓存"""
        cache = Cache(max_size=2, ttl=1)
        
        # 测试设置和获取
        cache.set("key1", "value1")
        self.assertEqual(cache.get("key1"), "value1")
        
        # 测试大小限制
        cache.set("key2", "value2")
        cache.set("key3", "value3")  # 应该触发清理
        self.assertEqual(cache.size(), 2)
        
        # 测试TTL
        time.sleep(1.1)
        self.assertIsNone(cache.get("key1"))
    
    def test_text_utils(self):
        """测试文本工具"""
        # 测试文本清理
        dirty_text = "  Hello   world!  \n\t  "
        clean_text = TextUtils.clean_text(dirty_text)
        self.assertEqual(clean_text, "Hello world!")
        
        # 测试句子提取
        text = "Hello world! How are you? Fine, thanks."
        sentences = TextUtils.extract_sentences(text)
        self.assertEqual(len(sentences), 3)
        
        # 测试文本截断
        long_text = "This is a very long sentence that needs to be truncated"
        truncated = TextUtils.truncate_text(long_text, 20)
        self.assertEqual(len(truncated), 20)
        self.assertTrue(truncated.endswith("..."))
    
    def test_hash_utils(self):
        """测试哈希工具"""
        text = "test string"
        
        # MD5哈希应该是32字符的十六进制字符串
        md5_hash = HashUtils.md5_hash(text)
        self.assertEqual(len(md5_hash), 32)
        self.assertTrue(all(c in "0123456789abcdef" for c in md5_hash))
        
        # SHA256哈希应该是64字符的十六进制字符串
        sha256_hash = HashUtils.sha256_hash(text)
        self.assertEqual(len(sha256_hash), 64)
        
        # 简单哈希应该是整数
        simple_hash = HashUtils.simple_hash(text)
        self.assertIsInstance(simple_hash, int)
    
    def test_validation_utils(self):
        """测试验证工具"""
        # 邮箱验证
        self.assertTrue(ValidationUtils.is_valid_email("<EMAIL>"))
        self.assertFalse(ValidationUtils.is_valid_email("invalid-email"))
        
        # URL验证
        self.assertTrue(ValidationUtils.is_valid_url("https://example.com"))
        self.assertFalse(ValidationUtils.is_valid_url("not-a-url"))
        
        # 查询验证
        self.assertTrue(ValidationUtils.validate_query("What is contract law?"))
        self.assertFalse(ValidationUtils.validate_query(""))
        self.assertFalse(ValidationUtils.validate_query("ab"))  # 太短


class TestPerformance(unittest.TestCase):
    """性能测试"""
    
    def setUp(self):
        self.qa_system = LegalQASystem(nodes=["perf_node_1", "perf_node_2"])
    
    def test_query_performance(self):
        """测试查询性能"""
        query = "What is a breach of contract?"
        
        start_time = time.time()
        response = self.qa_system.ask(query)
        end_time = time.time()
        
        processing_time = end_time - start_time
        
        # 查询应该在合理时间内完成（5秒内）
        self.assertLess(processing_time, 5.0)
        
        # 响应时间应该与报告的处理时间接近
        self.assertAlmostEqual(processing_time, response.processing_time, delta=0.1)
    
    def test_concurrent_queries(self):
        """测试并发查询（简单版本）"""
        queries = [
            "What is contract law?",
            "What is tort law?",
            "What is criminal law?"
        ]
        
        start_time = time.time()
        responses = []
        
        for query in queries:
            response = self.qa_system.ask(query)
            responses.append(response)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # 所有查询都应该成功
        self.assertEqual(len(responses), len(queries))
        for response in responses:
            self.assertIsNotNone(response.answer)
            self.assertGreater(len(response.answer), 0)
        
        # 总时间应该合理
        self.assertLess(total_time, 15.0)  # 3个查询在15秒内完成


def run_all_tests():
    """运行所有测试"""
    print("=== 运行法律问答系统测试套件 ===\n")
    
    # 创建测试套件
    test_classes = [
        TestConsistentHash,
        TestLegalAgent,
        TestVectorStore,
        TestRAGRetriever,
        TestLegalKnowledgeBase,
        TestUtils,
        TestLegalQASystem,
        TestPerformance
    ]
    
    total_tests = 0
    total_failures = 0
    total_errors = 0
    
    for test_class in test_classes:
        print(f"运行 {test_class.__name__} 测试...")
        
        suite = unittest.TestLoader().loadTestsFromTestCase(test_class)
        runner = unittest.TextTestRunner(verbosity=1)
        result = runner.run(suite)
        
        total_tests += result.testsRun
        total_failures += len(result.failures)
        total_errors += len(result.errors)
        
        if result.failures:
            print(f"  失败: {len(result.failures)}")
        if result.errors:
            print(f"  错误: {len(result.errors)}")
        
        print()
    
    # 总结
    print("=" * 50)
    print("测试总结:")
    print(f"  总测试数: {total_tests}")
    print(f"  成功: {total_tests - total_failures - total_errors}")
    print(f"  失败: {total_failures}")
    print(f"  错误: {total_errors}")
    
    if total_failures == 0 and total_errors == 0:
        print("\n🎉 所有测试通过!")
    else:
        print(f"\n❌ 有 {total_failures + total_errors} 个测试未通过")
    
    return total_failures + total_errors == 0


if __name__ == "__main__":
    success = run_all_tests()
    exit(0 if success else 1)
