# 美国法律问答系统

一个基于Agent分析、一致性哈希和RAG检索的智能法律问答系统，专门针对美国法律领域设计。

## 🌟 系统特性

### 核心功能
- **智能Agent分析**: 自动识别法律领域、查询类型和关键实体
- **一致性哈希分布**: 支持分布式部署和负载均衡
- **RAG检索系统**: 基于向量相似度的智能文档检索
- **法律知识库**: 涵盖美国主要法律领域的专业文档
- **多维度搜索**: 支持按领域、类型、权威级别等多种维度过滤

### 支持的法律领域
- 合同法 (Contract Law)
- 侵权法 (Tort Law)  
- 刑法 (Criminal Law)
- 宪法 (Constitutional Law)
- 财产法 (Property Law)
- 公司法 (Corporate Law)
- 家庭法 (Family Law)
- 劳动法 (Employment Law)
- 知识产权法 (Intellectual Property)
- 税法 (Tax Law)
- 移民法 (Immigration Law)
- 环境法 (Environmental Law)

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户查询      │───▶│   Legal Agent   │───▶│   一致性哈希    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │                        │
                              ▼                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   查询分析      │    │   RAG检索器     │    │   分布式存储    │
│   - 领域识别    │    │   - 向量搜索    │    │   - 节点路由    │
│   - 类型分类    │    │   - 相似度计算  │    │   - 负载均衡    │
│   - 实体提取    │    │   - 结果排序    │    │   - 故障转移    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
┌─────────────────┐    ┌─────────────────┐
│   法律知识库    │    │   答案生成      │
│   - 法条文档    │    │   - 内容整合    │
│   - 案例法      │    │   - 来源标注    │
│   - 法规条文    │    │   - 置信度评估  │
└─────────────────┘    └─────────────────┘
```

## 📦 安装和配置

### 环境要求
- Python 3.7+
- 内存: 至少 2GB RAM
- 存储: 至少 1GB 可用空间

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd legal-qa-system
```

2. **创建虚拟环境**
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

4. **创建必要目录**
```bash
mkdir -p data logs cache
```

## 🚀 快速开始

### 基本使用

```python
from legal_qa_system import LegalQASystem

# 初始化系统
qa_system = LegalQASystem()

# 提问
response = qa_system.ask("What is a breach of contract?")

# 查看答案
print(f"问题: {response.query}")
print(f"答案: {response.answer}")
print(f"置信度: {response.confidence:.2f}")
print(f"处理时间: {response.processing_time:.2f}秒")

# 查看来源
for source in response.sources:
    print(f"来源: {source['title']} (相关度: {source['relevance_score']:.3f})")
```

### 运行演示

```bash
python legal_qa_system.py
```

### 运行测试

```bash
python test_legal_qa.py
```

## 📚 详细使用指南

### 1. 系统组件

#### Legal Agent (法律智能体)
负责分析用户查询，识别法律领域和查询类型：

```python
from legal_agent import LegalAgent

agent = LegalAgent()
analysis = agent.analyze_query("What are the elements of negligence?")

print(f"领域: {analysis.domain.value}")
print(f"类型: {analysis.query_type.value}")
print(f"关键词: {analysis.keywords}")
print(f"置信度: {analysis.confidence}")
```

#### 一致性哈希
用于分布式部署和负载均衡：

```python
from consistent_hash import ConsistentHash

# 创建哈希环
nodes = ["legal_server_1", "legal_server_2", "legal_server_3"]
ch = ConsistentHash(nodes)

# 查找查询应该路由到的节点
query = "contract law question"
target_node = ch.get_node(query)
print(f"查询路由到: {target_node}")
```

#### RAG检索器
基于向量相似度进行文档检索：

```python
from rag_retrieval import VectorStore, RAGRetriever
from legal_knowledge import LegalKnowledgeBase

# 加载知识库
kb = LegalKnowledgeBase()
documents = kb.get_all_documents()

# 创建向量存储
vector_store = VectorStore()
vector_store.add_documents(documents)

# 创建检索器
retriever = RAGRetriever(vector_store)

# 检索相关文档
search_strategy = {
    "keywords": ["contract", "breach"],
    "filters": {"domain": "contract_law"}
}
results = retriever.retrieve("breach of contract", search_strategy)
```

### 2. 配置系统

系统配置通过 `config.py` 管理：

```python
from config import Config

# 查看当前配置
config = Config.get_config()
print(config)

# 更新配置
Config.update_config('rag', {'top_k_default': 10})

# 环境变量配置
import os
os.environ['EMBEDDING_DIM'] = '300'
from config import EnvironmentConfig
EnvironmentConfig.apply_env_config()
```

### 3. 扩展知识库

添加新的法律文档：

```python
from rag_retrieval import Document
from legal_qa_system import LegalQASystem

# 创建新文档
new_doc = Document(
    id="custom_001",
    title="Custom Legal Document",
    content="Your legal content here...",
    metadata={
        "domain": "contract_law",
        "document_type": "statute",
        "authority": "federal",
        "jurisdiction": "United States"
    }
)

# 添加到系统
qa_system = LegalQASystem()
qa_system.vector_store.add_document(new_doc)
```

## 🔧 高级功能

### 分布式部署

```python
# 添加新节点
qa_system.add_node("new_legal_server")

# 移除节点
qa_system.remove_node("old_legal_server")

# 查看分布式状态
stats = qa_system.get_system_stats()
print(stats['distributed_storage'])
```

### 性能监控

```python
# 查看系统统计
stats = qa_system.get_system_stats()
print(f"总查询数: {stats['query_statistics']['total_queries']}")
print(f"平均处理时间: {stats['query_statistics']['average_processing_time']:.2f}秒")
```

### 缓存优化

```python
from utils import Cache, cache_decorator

# 创建缓存
cache = Cache(max_size=1000, ttl=3600)

# 使用缓存装饰器
@cache_decorator(cache)
def expensive_operation(query):
    # 耗时操作
    return result
```

## 📊 性能指标

### 基准测试结果
- **查询响应时间**: 平均 0.5-2.0 秒
- **并发处理能力**: 支持 10+ 并发查询
- **准确率**: 在测试集上达到 85%+ 的相关性匹配
- **知识库规模**: 包含 20+ 个核心法律文档
- **内存使用**: 运行时约 500MB-1GB

### 扩展性
- **水平扩展**: 支持通过一致性哈希添加更多节点
- **知识库扩展**: 支持动态添加新的法律文档
- **领域扩展**: 可以轻松添加新的法律领域

## 🧪 测试

### 运行完整测试套件
```bash
python test_legal_qa.py
```

### 单独测试组件
```bash
python -m unittest test_legal_qa.TestConsistentHash
python -m unittest test_legal_qa.TestLegalAgent
python -m unittest test_legal_qa.TestRAGRetriever
```

### 性能测试
```bash
python -m unittest test_legal_qa.TestPerformance
```

## 🔍 故障排除

### 常见问题

1. **内存不足错误**
   - 减少向量维度: 修改 `config.py` 中的 `embedding_dim`
   - 减少文档数量: 过滤知识库文档

2. **查询响应慢**
   - 启用缓存: 使用 `Cache` 类缓存查询结果
   - 优化向量存储: 调整 TF-IDF 参数

3. **准确率低**
   - 增加知识库文档
   - 调整搜索策略参数
   - 优化关键词提取

### 调试模式

```python
from config import Config
Config.DEBUG = True

# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 🤝 贡献指南

### 开发环境设置
1. Fork 项目
2. 创建功能分支: `git checkout -b feature/new-feature`
3. 安装开发依赖: `pip install -r requirements.txt`
4. 运行测试确保一切正常

### 代码规范
- 使用 Black 进行代码格式化
- 使用 flake8 进行代码检查
- 编写单元测试覆盖新功能
- 更新文档说明

### 提交流程
1. 确保所有测试通过
2. 提交代码: `git commit -m "Add new feature"`
3. 推送分支: `git push origin feature/new-feature`
4. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持和联系

- **问题报告**: 请在 GitHub Issues 中提交
- **功能请求**: 请在 GitHub Issues 中标记为 enhancement
- **文档问题**: 请在 GitHub Issues 中标记为 documentation

## 🗺️ 路线图

### 短期目标 (1-3个月)
- [ ] 添加更多法律领域文档
- [ ] 优化查询响应时间
- [ ] 增加 Web API 接口
- [ ] 改进答案生成质量

### 中期目标 (3-6个月)
- [ ] 集成大语言模型 (LLM)
- [ ] 添加多语言支持
- [ ] 实现实时学习功能
- [ ] 增加可视化界面

### 长期目标 (6-12个月)
- [ ] 支持复杂法律推理
- [ ] 集成外部法律数据库
- [ ] 提供专业法律建议
- [ ] 开发移动应用

---

**注意**: 本系统仅供教育和研究目的使用，不应作为正式的法律建议。如需专业法律咨询，请联系合格的律师。
