"""
RAG检索模块
实现向量化文档存储和相似度搜索
"""

import numpy as np
import json
import pickle
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.decomposition import TruncatedSVD
import re


@dataclass
class Document:
    """文档数据结构"""
    id: str
    title: str
    content: str
    metadata: Dict[str, Any]
    vector: Optional[np.ndarray] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'title': self.title,
            'content': self.content,
            'metadata': self.metadata
        }


@dataclass
class SearchResult:
    """搜索结果"""
    document: Document
    score: float
    highlights: List[str]
    explanation: str


class TextProcessor:
    """文本处理器"""
    
    def __init__(self):
        self.stop_words = self._load_stop_words()
    
    def _load_stop_words(self) -> set:
        """加载停用词"""
        # 简化的英文停用词列表
        return {
            'a', 'an', 'and', 'are', 'as', 'at', 'be', 'by', 'for', 'from',
            'has', 'he', 'in', 'is', 'it', 'its', 'of', 'on', 'that', 'the',
            'to', 'was', 'will', 'with', 'would', 'could', 'should', 'may',
            'might', 'can', 'shall', 'must', 'ought', 'need', 'dare'
        }
    
    def preprocess_text(self, text: str) -> str:
        """预处理文本"""
        # 转换为小写
        text = text.lower()
        
        # 移除特殊字符，保留字母、数字和空格
        text = re.sub(r'[^a-zA-Z0-9\s]', ' ', text)
        
        # 移除多余空格
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def tokenize(self, text: str) -> List[str]:
        """分词"""
        text = self.preprocess_text(text)
        tokens = text.split()
        
        # 移除停用词
        tokens = [token for token in tokens if token not in self.stop_words]
        
        return tokens
    
    def extract_keywords(self, text: str, top_k: int = 10) -> List[str]:
        """提取关键词"""
        tokens = self.tokenize(text)
        
        # 简单的词频统计
        word_freq = {}
        for token in tokens:
            word_freq[token] = word_freq.get(token, 0) + 1
        
        # 按频率排序
        sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
        
        return [word for word, freq in sorted_words[:top_k]]


class VectorStore:
    """向量存储"""
    
    def __init__(self, embedding_dim: int = 300):
        self.embedding_dim = embedding_dim
        self.documents = {}  # id -> Document
        self.vectors = {}    # id -> vector
        self.vectorizer = TfidfVectorizer(
            max_features=5000,
            stop_words='english',
            ngram_range=(1, 2)
        )
        self.svd = TruncatedSVD(n_components=embedding_dim)
        self.is_fitted = False
        self.text_processor = TextProcessor()
    
    def add_document(self, document: Document) -> None:
        """添加文档"""
        self.documents[document.id] = document
        
        # 如果已经训练过模型，需要重新训练
        if self.is_fitted:
            self._refit_model()
    
    def add_documents(self, documents: List[Document]) -> None:
        """批量添加文档"""
        for doc in documents:
            self.documents[doc.id] = doc
        
        self._fit_model()
    
    def _fit_model(self) -> None:
        """训练向量化模型"""
        if not self.documents:
            return
        
        # 准备文本数据
        texts = []
        doc_ids = []
        
        for doc_id, doc in self.documents.items():
            # 合并标题和内容
            full_text = f"{doc.title} {doc.content}"
            texts.append(full_text)
            doc_ids.append(doc_id)
        
        # TF-IDF向量化
        tfidf_matrix = self.vectorizer.fit_transform(texts)
        
        # 降维
        reduced_vectors = self.svd.fit_transform(tfidf_matrix)
        
        # 存储向量
        for i, doc_id in enumerate(doc_ids):
            self.vectors[doc_id] = reduced_vectors[i]
            self.documents[doc_id].vector = reduced_vectors[i]
        
        self.is_fitted = True
    
    def _refit_model(self) -> None:
        """重新训练模型"""
        self._fit_model()
    
    def vectorize_query(self, query: str) -> np.ndarray:
        """向量化查询"""
        if not self.is_fitted:
            raise ValueError("模型尚未训练，请先添加文档")
        
        # TF-IDF向量化
        query_tfidf = self.vectorizer.transform([query])
        
        # 降维
        query_vector = self.svd.transform(query_tfidf)
        
        return query_vector[0]
    
    def search(self, query: str, top_k: int = 10, 
               filters: Dict[str, Any] = None) -> List[Tuple[str, float]]:
        """向量搜索"""
        if not self.is_fitted:
            return []
        
        # 向量化查询
        query_vector = self.vectorize_query(query)
        
        # 计算相似度
        similarities = []
        for doc_id, doc_vector in self.vectors.items():
            # 应用过滤器
            if filters and not self._apply_filters(self.documents[doc_id], filters):
                continue
            
            similarity = cosine_similarity(
                query_vector.reshape(1, -1),
                doc_vector.reshape(1, -1)
            )[0][0]
            
            similarities.append((doc_id, similarity))
        
        # 按相似度排序
        similarities.sort(key=lambda x: x[1], reverse=True)
        
        return similarities[:top_k]
    
    def _apply_filters(self, document: Document, filters: Dict[str, Any]) -> bool:
        """应用过滤器"""
        for key, value in filters.items():
            if key in document.metadata:
                if isinstance(value, list):
                    if document.metadata[key] not in value:
                        return False
                else:
                    if document.metadata[key] != value:
                        return False
        return True
    
    def get_document(self, doc_id: str) -> Optional[Document]:
        """获取文档"""
        return self.documents.get(doc_id)
    
    def save(self, filepath: str) -> None:
        """保存向量存储"""
        data = {
            'documents': {doc_id: doc.to_dict() for doc_id, doc in self.documents.items()},
            'vectors': {doc_id: vector.tolist() for doc_id, vector in self.vectors.items()},
            'vectorizer': self.vectorizer,
            'svd': self.svd,
            'is_fitted': self.is_fitted,
            'embedding_dim': self.embedding_dim
        }
        
        with open(filepath, 'wb') as f:
            pickle.dump(data, f)
    
    def load(self, filepath: str) -> None:
        """加载向量存储"""
        with open(filepath, 'rb') as f:
            data = pickle.load(f)
        
        # 重建文档
        self.documents = {}
        for doc_id, doc_data in data['documents'].items():
            doc = Document(
                id=doc_data['id'],
                title=doc_data['title'],
                content=doc_data['content'],
                metadata=doc_data['metadata']
            )
            self.documents[doc_id] = doc
        
        # 重建向量
        self.vectors = {doc_id: np.array(vector) for doc_id, vector in data['vectors'].items()}
        
        # 重建模型
        self.vectorizer = data['vectorizer']
        self.svd = data['svd']
        self.is_fitted = data['is_fitted']
        self.embedding_dim = data['embedding_dim']
        
        # 重新关联向量到文档
        for doc_id, vector in self.vectors.items():
            if doc_id in self.documents:
                self.documents[doc_id].vector = vector


class RAGRetriever:
    """RAG检索器"""
    
    def __init__(self, vector_store: VectorStore):
        self.vector_store = vector_store
        self.text_processor = TextProcessor()
    
    def retrieve(self, query: str, search_strategy: Dict[str, Any], 
                top_k: int = 5) -> List[SearchResult]:
        """检索相关文档"""
        # 提取搜索参数
        filters = search_strategy.get('filters', {})
        boost_terms = search_strategy.get('boost_terms', [])
        keywords = search_strategy.get('keywords', [])
        
        # 增强查询
        enhanced_query = self._enhance_query(query, boost_terms, keywords)
        
        # 向量搜索
        search_results = self.vector_store.search(enhanced_query, top_k * 2, filters)
        
        # 重新排序和过滤
        final_results = []
        for doc_id, score in search_results[:top_k]:
            document = self.vector_store.get_document(doc_id)
            if document:
                # 生成高亮
                highlights = self._generate_highlights(document, query, keywords)
                
                # 生成解释
                explanation = self._generate_explanation(document, query, score)
                
                result = SearchResult(
                    document=document,
                    score=score,
                    highlights=highlights,
                    explanation=explanation
                )
                final_results.append(result)
        
        return final_results
    
    def _enhance_query(self, query: str, boost_terms: List[str], 
                      keywords: List[str]) -> str:
        """增强查询"""
        enhanced_parts = [query]
        
        # 添加提升词
        for term in boost_terms:
            enhanced_parts.append(term)
        
        # 添加关键词
        for keyword in keywords:
            enhanced_parts.append(keyword)
        
        return ' '.join(enhanced_parts)
    
    def _generate_highlights(self, document: Document, query: str, 
                           keywords: List[str]) -> List[str]:
        """生成高亮片段"""
        highlights = []
        
        # 合并查询词和关键词
        search_terms = self.text_processor.tokenize(query) + keywords
        search_terms = list(set(search_terms))  # 去重
        
        # 在文档内容中查找匹配片段
        content = document.content
        sentences = content.split('.')
        
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) < 20:  # 跳过太短的句子
                continue
            
            # 检查是否包含搜索词
            sentence_lower = sentence.lower()
            for term in search_terms:
                if term.lower() in sentence_lower:
                    # 限制高亮长度
                    if len(sentence) > 200:
                        # 找到匹配词的位置，截取周围文本
                        pos = sentence_lower.find(term.lower())
                        start = max(0, pos - 100)
                        end = min(len(sentence), pos + 100)
                        highlight = "..." + sentence[start:end] + "..."
                    else:
                        highlight = sentence
                    
                    highlights.append(highlight)
                    break
        
        return highlights[:3]  # 最多返回3个高亮
    
    def _generate_explanation(self, document: Document, query: str, 
                            score: float) -> str:
        """生成匹配解释"""
        explanations = []
        
        # 相似度解释
        if score > 0.8:
            explanations.append("高度相关")
        elif score > 0.6:
            explanations.append("相关")
        elif score > 0.4:
            explanations.append("部分相关")
        else:
            explanations.append("低相关度")
        
        # 文档类型解释
        doc_type = document.metadata.get('document_type', 'unknown')
        if doc_type == 'case':
            explanations.append("案例法文档")
        elif doc_type == 'statute':
            explanations.append("法条文档")
        elif doc_type == 'regulation':
            explanations.append("法规文档")
        
        # 权威性解释
        authority = document.metadata.get('authority', 'unknown')
        if authority == 'supreme_court':
            explanations.append("最高法院文档")
        elif authority == 'federal':
            explanations.append("联邦级文档")
        elif authority == 'state':
            explanations.append("州级文档")
        
        return " | ".join(explanations)


if __name__ == "__main__":
    # 测试RAG检索
    print("=== RAG检索测试 ===\n")
    
    # 创建测试文档
    test_documents = [
        Document(
            id="doc_001",
            title="Contract Law Basics",
            content="A contract is a legally binding agreement between two or more parties. The essential elements of a contract include offer, acceptance, consideration, and mutual assent. A breach of contract occurs when one party fails to perform their obligations under the agreement.",
            metadata={
                "document_type": "statute",
                "domain": "contract_law",
                "authority": "federal",
                "date": "2023-01-01"
            }
        ),
        Document(
            id="doc_002",
            title="Negligence in Tort Law",
            content="Negligence is a failure to exercise the care that a reasonably prudent person would exercise in like circumstances. The elements of negligence include duty, breach of duty, causation, and damages. To prove negligence, the plaintiff must establish all four elements.",
            metadata={
                "document_type": "case",
                "domain": "tort_law",
                "authority": "state",
                "date": "2023-02-01"
            }
        ),
        Document(
            id="doc_003",
            title="Fourth Amendment Rights",
            content="The Fourth Amendment protects against unreasonable searches and seizures. It requires that warrants be supported by probable cause and describe the place to be searched and the persons or things to be seized. The exclusionary rule prevents illegally obtained evidence from being used in court.",
            metadata={
                "document_type": "statute",
                "domain": "constitutional_law",
                "authority": "supreme_court",
                "date": "2023-03-01"
            }
        ),
        Document(
            id="doc_004",
            title="Criminal Law Fundamentals",
            content="Criminal law defines crimes and prescribes punishments. A crime typically consists of two elements: actus reus (guilty act) and mens rea (guilty mind). The prosecution must prove both elements beyond a reasonable doubt to secure a conviction.",
            metadata={
                "document_type": "regulation",
                "domain": "criminal_law",
                "authority": "federal",
                "date": "2023-04-01"
            }
        ),
        Document(
            id="doc_005",
            title="Property Law Overview",
            content="Property law governs the various forms of ownership and tenancy in real and personal property. It includes concepts such as fee simple, life estate, easements, and adverse possession. Property rights are fundamental to the legal system.",
            metadata={
                "document_type": "statute",
                "domain": "property_law",
                "authority": "state",
                "date": "2023-05-01"
            }
        )
    ]
    
    # 创建向量存储
    vector_store = VectorStore(embedding_dim=100)
    vector_store.add_documents(test_documents)
    
    # 创建检索器
    retriever = RAGRetriever(vector_store)
    
    # 测试查询
    test_queries = [
        {
            "query": "What is a breach of contract?",
            "strategy": {
                "filters": {"domain": "contract_law"},
                "boost_terms": ["breach", "contract"],
                "keywords": ["agreement", "obligation"]
            }
        },
        {
            "query": "Elements of negligence",
            "strategy": {
                "filters": {"domain": "tort_law"},
                "boost_terms": ["negligence", "elements"],
                "keywords": ["duty", "breach", "causation", "damages"]
            }
        },
        {
            "query": "Fourth Amendment search and seizure",
            "strategy": {
                "filters": {"domain": "constitutional_law"},
                "boost_terms": ["fourth amendment", "search", "seizure"],
                "keywords": ["warrant", "probable cause"]
            }
        }
    ]
    
    for i, test_case in enumerate(test_queries, 1):
        print(f"查询 {i}: {test_case['query']}")
        results = retriever.retrieve(
            test_case['query'], 
            test_case['strategy'], 
            top_k=3
        )
        
        for j, result in enumerate(results, 1):
            print(f"  结果 {j}:")
            print(f"    文档: {result.document.title}")
            print(f"    相似度: {result.score:.3f}")
            print(f"    解释: {result.explanation}")
            if result.highlights:
                print(f"    高亮: {result.highlights[0][:100]}...")
            print()
        
        print("-" * 50)
