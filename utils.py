"""
通用工具函数
"""

import re
import json
import time
import hashlib
import logging
from typing import List, Dict, Any, Optional, Union, Callable
from functools import wraps
from datetime import datetime, timedelta
import pickle
import os


class Timer:
    """计时器工具"""
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
    
    def start(self):
        """开始计时"""
        self.start_time = time.time()
        return self
    
    def stop(self):
        """停止计时"""
        self.end_time = time.time()
        return self
    
    def elapsed(self) -> float:
        """获取经过的时间"""
        if self.start_time is None:
            return 0.0
        
        end = self.end_time if self.end_time else time.time()
        return end - self.start_time
    
    def __enter__(self):
        """上下文管理器入口"""
        return self.start()
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.stop()


def timing_decorator(func: Callable) -> Callable:
    """计时装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        print(f"{func.__name__} 执行时间: {end_time - start_time:.4f}秒")
        return result
    
    return wrapper


class Cache:
    """简单的内存缓存"""
    
    def __init__(self, max_size: int = 1000, ttl: int = 3600):
        """
        初始化缓存
        
        Args:
            max_size: 最大缓存条目数
            ttl: 生存时间（秒）
        """
        self.max_size = max_size
        self.ttl = ttl
        self.cache = {}
        self.access_times = {}
    
    def _is_expired(self, key: str) -> bool:
        """检查缓存项是否过期"""
        if key not in self.access_times:
            return True
        
        return time.time() - self.access_times[key] > self.ttl
    
    def _evict_if_needed(self):
        """如果需要，清理缓存"""
        # 清理过期项
        expired_keys = [k for k in self.cache.keys() if self._is_expired(k)]
        for key in expired_keys:
            self.remove(key)
        
        # 如果仍然超过大小限制，使用LRU策略
        if len(self.cache) >= self.max_size:
            # 找到最久未访问的项
            oldest_key = min(self.access_times.keys(), key=self.access_times.get)
            self.remove(oldest_key)
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        if key not in self.cache or self._is_expired(key):
            return None
        
        self.access_times[key] = time.time()
        return self.cache[key]
    
    def set(self, key: str, value: Any) -> None:
        """设置缓存值"""
        self._evict_if_needed()
        self.cache[key] = value
        self.access_times[key] = time.time()
    
    def remove(self, key: str) -> None:
        """移除缓存项"""
        self.cache.pop(key, None)
        self.access_times.pop(key, None)
    
    def clear(self) -> None:
        """清空缓存"""
        self.cache.clear()
        self.access_times.clear()
    
    def size(self) -> int:
        """获取缓存大小"""
        return len(self.cache)
    
    def stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        return {
            'size': len(self.cache),
            'max_size': self.max_size,
            'ttl': self.ttl,
            'keys': list(self.cache.keys())
        }


def cache_decorator(cache_instance: Cache, key_func: Callable = None):
    """缓存装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"
            
            # 尝试从缓存获取
            result = cache_instance.get(cache_key)
            if result is not None:
                return result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache_instance.set(cache_key, result)
            return result
        
        return wrapper
    return decorator


class TextUtils:
    """文本处理工具"""
    
    @staticmethod
    def clean_text(text: str) -> str:
        """清理文本"""
        if not text:
            return ""
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 移除特殊字符（保留基本标点）
        text = re.sub(r'[^\w\s\.\,\!\?\;\:\-\(\)]', '', text)
        
        return text.strip()
    
    @staticmethod
    def extract_sentences(text: str) -> List[str]:
        """提取句子"""
        # 简单的句子分割
        sentences = re.split(r'[.!?]+', text)
        return [s.strip() for s in sentences if s.strip()]
    
    @staticmethod
    def truncate_text(text: str, max_length: int, suffix: str = "...") -> str:
        """截断文本"""
        if len(text) <= max_length:
            return text
        
        return text[:max_length - len(suffix)] + suffix
    
    @staticmethod
    def highlight_keywords(text: str, keywords: List[str], 
                          start_tag: str = "<mark>", end_tag: str = "</mark>") -> str:
        """高亮关键词"""
        highlighted_text = text
        
        for keyword in keywords:
            pattern = re.compile(re.escape(keyword), re.IGNORECASE)
            highlighted_text = pattern.sub(
                f"{start_tag}{keyword}{end_tag}", 
                highlighted_text
            )
        
        return highlighted_text
    
    @staticmethod
    def calculate_similarity(text1: str, text2: str) -> float:
        """计算文本相似度（简单的Jaccard相似度）"""
        if not text1 or not text2:
            return 0.0
        
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        if not union:
            return 0.0
        
        return len(intersection) / len(union)


class HashUtils:
    """哈希工具"""
    
    @staticmethod
    def md5_hash(text: str) -> str:
        """计算MD5哈希"""
        return hashlib.md5(text.encode('utf-8')).hexdigest()
    
    @staticmethod
    def sha256_hash(text: str) -> str:
        """计算SHA256哈希"""
        return hashlib.sha256(text.encode('utf-8')).hexdigest()
    
    @staticmethod
    def simple_hash(text: str) -> int:
        """简单哈希函数"""
        return hash(text) % (2**32)


class FileUtils:
    """文件工具"""
    
    @staticmethod
    def save_json(data: Any, filepath: str, indent: int = 2) -> bool:
        """保存JSON文件"""
        try:
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=indent, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"保存JSON文件失败: {e}")
            return False
    
    @staticmethod
    def load_json(filepath: str) -> Optional[Any]:
        """加载JSON文件"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载JSON文件失败: {e}")
            return None
    
    @staticmethod
    def save_pickle(data: Any, filepath: str) -> bool:
        """保存Pickle文件"""
        try:
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            with open(filepath, 'wb') as f:
                pickle.dump(data, f)
            return True
        except Exception as e:
            print(f"保存Pickle文件失败: {e}")
            return False
    
    @staticmethod
    def load_pickle(filepath: str) -> Optional[Any]:
        """加载Pickle文件"""
        try:
            with open(filepath, 'rb') as f:
                return pickle.load(f)
        except Exception as e:
            print(f"加载Pickle文件失败: {e}")
            return None
    
    @staticmethod
    def ensure_directory(filepath: str) -> None:
        """确保目录存在"""
        directory = os.path.dirname(filepath)
        if directory:
            os.makedirs(directory, exist_ok=True)


class LoggerUtils:
    """日志工具"""
    
    @staticmethod
    def setup_logger(name: str, log_file: str = None, level: str = 'INFO') -> logging.Logger:
        """设置日志器"""
        logger = logging.getLogger(name)
        logger.setLevel(getattr(logging, level.upper()))
        
        # 清除现有处理器
        logger.handlers.clear()
        
        # 创建格式器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        # 文件处理器
        if log_file:
            FileUtils.ensure_directory(log_file)
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
        
        return logger


class ValidationUtils:
    """验证工具"""
    
    @staticmethod
    def is_valid_email(email: str) -> bool:
        """验证邮箱格式"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    @staticmethod
    def is_valid_url(url: str) -> bool:
        """验证URL格式"""
        pattern = r'^https?://[^\s/$.?#].[^\s]*$'
        return re.match(pattern, url) is not None
    
    @staticmethod
    def validate_query(query: str, min_length: int = 3, max_length: int = 1000) -> bool:
        """验证查询字符串"""
        if not query or not isinstance(query, str):
            return False
        
        query = query.strip()
        return min_length <= len(query) <= max_length


class MetricsCollector:
    """指标收集器"""
    
    def __init__(self):
        self.metrics = {}
        self.start_time = time.time()
    
    def increment(self, metric_name: str, value: Union[int, float] = 1) -> None:
        """增加指标值"""
        if metric_name not in self.metrics:
            self.metrics[metric_name] = 0
        self.metrics[metric_name] += value
    
    def set_gauge(self, metric_name: str, value: Union[int, float]) -> None:
        """设置仪表盘指标"""
        self.metrics[metric_name] = value
    
    def record_timing(self, metric_name: str, duration: float) -> None:
        """记录时间指标"""
        timing_key = f"{metric_name}_timing"
        if timing_key not in self.metrics:
            self.metrics[timing_key] = []
        self.metrics[timing_key].append(duration)
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取所有指标"""
        current_time = time.time()
        uptime = current_time - self.start_time
        
        result = self.metrics.copy()
        result['uptime_seconds'] = uptime
        result['collection_time'] = datetime.now().isoformat()
        
        # 计算时间指标的统计信息
        for key, value in self.metrics.items():
            if key.endswith('_timing') and isinstance(value, list):
                if value:
                    result[f"{key}_avg"] = sum(value) / len(value)
                    result[f"{key}_min"] = min(value)
                    result[f"{key}_max"] = max(value)
                    result[f"{key}_count"] = len(value)
        
        return result
    
    def reset(self) -> None:
        """重置指标"""
        self.metrics.clear()
        self.start_time = time.time()


def retry_decorator(max_retries: int = 3, delay: float = 1.0, 
                   exceptions: tuple = (Exception,)):
    """重试装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt < max_retries:
                        time.sleep(delay * (2 ** attempt))  # 指数退避
                        continue
                    else:
                        raise last_exception
            
            return None
        
        return wrapper
    return decorator


if __name__ == "__main__":
    # 测试工具函数
    print("=== 工具函数测试 ===\n")
    
    # 测试计时器
    print("1. 计时器测试:")
    with Timer() as timer:
        time.sleep(0.1)
    print(f"   经过时间: {timer.elapsed():.3f}秒\n")
    
    # 测试缓存
    print("2. 缓存测试:")
    cache = Cache(max_size=3, ttl=2)
    cache.set("key1", "value1")
    cache.set("key2", "value2")
    print(f"   缓存大小: {cache.size()}")
    print(f"   获取key1: {cache.get('key1')}")
    print(f"   缓存统计: {cache.stats()}\n")
    
    # 测试文本工具
    print("3. 文本工具测试:")
    text = "  This is a test   sentence with    extra spaces!  "
    cleaned = TextUtils.clean_text(text)
    print(f"   原文: '{text}'")
    print(f"   清理后: '{cleaned}'")
    
    sentences = TextUtils.extract_sentences("Hello world! How are you? Fine, thanks.")
    print(f"   句子提取: {sentences}")
    
    truncated = TextUtils.truncate_text("This is a very long sentence", 15)
    print(f"   截断文本: '{truncated}'\n")
    
    # 测试哈希工具
    print("4. 哈希工具测试:")
    test_text = "Hello, World!"
    print(f"   MD5: {HashUtils.md5_hash(test_text)}")
    print(f"   SHA256: {HashUtils.sha256_hash(test_text)[:16]}...")
    print(f"   简单哈希: {HashUtils.simple_hash(test_text)}\n")
    
    # 测试验证工具
    print("5. 验证工具测试:")
    print(f"   邮箱验证 '<EMAIL>': {ValidationUtils.is_valid_email('<EMAIL>')}")
    print(f"   URL验证 'https://example.com': {ValidationUtils.is_valid_url('https://example.com')}")
    print(f"   查询验证 'What is contract law?': {ValidationUtils.validate_query('What is contract law?')}\n")
    
    # 测试指标收集器
    print("6. 指标收集器测试:")
    metrics = MetricsCollector()
    metrics.increment("queries_processed")
    metrics.increment("queries_processed", 2)
    metrics.set_gauge("active_connections", 5)
    metrics.record_timing("query_duration", 0.5)
    metrics.record_timing("query_duration", 0.8)
    
    print(f"   指标: {metrics.get_metrics()}")
    
    print("\n工具函数测试完成!")
