"""
系统配置文件
"""

import os
from typing import Dict, Any, List


class Config:
    """系统配置类"""
    
    # 系统基本配置
    SYSTEM_NAME = "美国法律问答系统"
    VERSION = "1.0.0"
    DEBUG = True
    
    # 一致性哈希配置
    CONSISTENT_HASH_CONFIG = {
        'replicas': 150,  # 每个物理节点的虚拟节点数量
        'default_nodes': [
            'legal_node_1',
            'legal_node_2', 
            'legal_node_3',
            'legal_node_4'
        ]
    }
    
    # 向量存储配置
    VECTOR_STORE_CONFIG = {
        'embedding_dim': 200,  # 向量维度
        'max_features': 5000,  # TF-IDF最大特征数
        'ngram_range': (1, 2),  # N-gram范围
        'min_df': 1,  # 最小文档频率
        'max_df': 0.95  # 最大文档频率
    }
    
    # RAG检索配置
    RAG_CONFIG = {
        'top_k_default': 5,  # 默认返回的文档数量
        'similarity_threshold': 0.1,  # 相似度阈值
        'max_highlights': 3,  # 最大高亮数量
        'highlight_length': 200  # 高亮片段长度
    }
    
    # Agent配置
    AGENT_CONFIG = {
        'confidence_threshold': 0.5,  # 置信度阈值
        'max_keywords': 10,  # 最大关键词数量
        'max_entities': 5  # 最大实体数量
    }
    
    # 法律领域配置
    LEGAL_DOMAINS = [
        'contract_law',
        'tort_law',
        'criminal_law',
        'constitutional_law',
        'property_law',
        'corporate_law',
        'family_law',
        'employment_law',
        'intellectual_property',
        'tax_law',
        'immigration_law',
        'environmental_law'
    ]
    
    # 查询类型配置
    QUERY_TYPES = [
        'definition',
        'procedure',
        'case_law',
        'statute',
        'precedent',
        'advice',
        'comparison'
    ]
    
    # 文档类型配置
    DOCUMENT_TYPES = [
        'statute',      # 法条
        'case',         # 案例
        'regulation',   # 法规
        'opinion',      # 意见书
        'brief',        # 摘要
        'commentary'    # 评论
    ]
    
    # 权威级别配置
    AUTHORITY_LEVELS = [
        'supreme_court',  # 最高法院
        'federal',        # 联邦级
        'state',          # 州级
        'local',          # 地方级
        'administrative'  # 行政级
    ]
    
    # 管辖区配置
    JURISDICTIONS = [
        'United States',
        'Federal',
        'Alabama', 'Alaska', 'Arizona', 'Arkansas', 'California',
        'Colorado', 'Connecticut', 'Delaware', 'Florida', 'Georgia',
        'Hawaii', 'Idaho', 'Illinois', 'Indiana', 'Iowa',
        'Kansas', 'Kentucky', 'Louisiana', 'Maine', 'Maryland',
        'Massachusetts', 'Michigan', 'Minnesota', 'Mississippi', 'Missouri',
        'Montana', 'Nebraska', 'Nevada', 'New Hampshire', 'New Jersey',
        'New Mexico', 'New York', 'North Carolina', 'North Dakota', 'Ohio',
        'Oklahoma', 'Oregon', 'Pennsylvania', 'Rhode Island', 'South Carolina',
        'South Dakota', 'Tennessee', 'Texas', 'Utah', 'Vermont',
        'Virginia', 'Washington', 'West Virginia', 'Wisconsin', 'Wyoming',
        'District of Columbia'
    ]
    
    # 性能配置
    PERFORMANCE_CONFIG = {
        'max_concurrent_queries': 10,  # 最大并发查询数
        'query_timeout': 30,  # 查询超时时间（秒）
        'cache_size': 1000,  # 缓存大小
        'cache_ttl': 3600  # 缓存生存时间（秒）
    }
    
    # 日志配置
    LOGGING_CONFIG = {
        'level': 'INFO',
        'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        'file': 'legal_qa_system.log',
        'max_size': 10 * 1024 * 1024,  # 10MB
        'backup_count': 5
    }
    
    # 数据存储配置
    STORAGE_CONFIG = {
        'vector_store_path': 'data/vector_store.pkl',
        'knowledge_base_path': 'data/knowledge_base.json',
        'cache_path': 'data/cache/',
        'logs_path': 'logs/'
    }
    
    # API配置（如果需要Web服务）
    API_CONFIG = {
        'host': '0.0.0.0',
        'port': 8000,
        'workers': 4,
        'timeout': 60,
        'max_request_size': 1024 * 1024,  # 1MB
        'cors_origins': ['*']
    }
    
    @classmethod
    def get_config(cls, section: str = None) -> Dict[str, Any]:
        """获取配置"""
        if section is None:
            return {
                'system': {
                    'name': cls.SYSTEM_NAME,
                    'version': cls.VERSION,
                    'debug': cls.DEBUG
                },
                'consistent_hash': cls.CONSISTENT_HASH_CONFIG,
                'vector_store': cls.VECTOR_STORE_CONFIG,
                'rag': cls.RAG_CONFIG,
                'agent': cls.AGENT_CONFIG,
                'performance': cls.PERFORMANCE_CONFIG,
                'logging': cls.LOGGING_CONFIG,
                'storage': cls.STORAGE_CONFIG,
                'api': cls.API_CONFIG
            }
        
        config_map = {
            'consistent_hash': cls.CONSISTENT_HASH_CONFIG,
            'vector_store': cls.VECTOR_STORE_CONFIG,
            'rag': cls.RAG_CONFIG,
            'agent': cls.AGENT_CONFIG,
            'performance': cls.PERFORMANCE_CONFIG,
            'logging': cls.LOGGING_CONFIG,
            'storage': cls.STORAGE_CONFIG,
            'api': cls.API_CONFIG
        }
        
        return config_map.get(section, {})
    
    @classmethod
    def update_config(cls, section: str, updates: Dict[str, Any]) -> None:
        """更新配置"""
        if section == 'consistent_hash':
            cls.CONSISTENT_HASH_CONFIG.update(updates)
        elif section == 'vector_store':
            cls.VECTOR_STORE_CONFIG.update(updates)
        elif section == 'rag':
            cls.RAG_CONFIG.update(updates)
        elif section == 'agent':
            cls.AGENT_CONFIG.update(updates)
        elif section == 'performance':
            cls.PERFORMANCE_CONFIG.update(updates)
        elif section == 'logging':
            cls.LOGGING_CONFIG.update(updates)
        elif section == 'storage':
            cls.STORAGE_CONFIG.update(updates)
        elif section == 'api':
            cls.API_CONFIG.update(updates)
    
    @classmethod
    def create_directories(cls) -> None:
        """创建必要的目录"""
        directories = [
            'data',
            'logs',
            'cache',
            cls.STORAGE_CONFIG['cache_path'],
            os.path.dirname(cls.STORAGE_CONFIG['vector_store_path']),
            os.path.dirname(cls.STORAGE_CONFIG['knowledge_base_path']),
            cls.STORAGE_CONFIG['logs_path']
        ]
        
        for directory in directories:
            if directory and not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)
                print(f"创建目录: {directory}")


# 环境变量配置
class EnvironmentConfig:
    """环境变量配置"""
    
    @staticmethod
    def load_from_env() -> Dict[str, Any]:
        """从环境变量加载配置"""
        env_config = {}
        
        # 系统配置
        if os.getenv('DEBUG'):
            env_config['debug'] = os.getenv('DEBUG').lower() == 'true'
        
        # 向量存储配置
        if os.getenv('EMBEDDING_DIM'):
            env_config['embedding_dim'] = int(os.getenv('EMBEDDING_DIM'))
        
        if os.getenv('MAX_FEATURES'):
            env_config['max_features'] = int(os.getenv('MAX_FEATURES'))
        
        # RAG配置
        if os.getenv('TOP_K_DEFAULT'):
            env_config['top_k_default'] = int(os.getenv('TOP_K_DEFAULT'))
        
        if os.getenv('SIMILARITY_THRESHOLD'):
            env_config['similarity_threshold'] = float(os.getenv('SIMILARITY_THRESHOLD'))
        
        # 性能配置
        if os.getenv('MAX_CONCURRENT_QUERIES'):
            env_config['max_concurrent_queries'] = int(os.getenv('MAX_CONCURRENT_QUERIES'))
        
        if os.getenv('QUERY_TIMEOUT'):
            env_config['query_timeout'] = int(os.getenv('QUERY_TIMEOUT'))
        
        # API配置
        if os.getenv('API_HOST'):
            env_config['api_host'] = os.getenv('API_HOST')
        
        if os.getenv('API_PORT'):
            env_config['api_port'] = int(os.getenv('API_PORT'))
        
        return env_config
    
    @staticmethod
    def apply_env_config() -> None:
        """应用环境变量配置"""
        env_config = EnvironmentConfig.load_from_env()
        
        if 'debug' in env_config:
            Config.DEBUG = env_config['debug']
        
        if 'embedding_dim' in env_config:
            Config.VECTOR_STORE_CONFIG['embedding_dim'] = env_config['embedding_dim']
        
        if 'max_features' in env_config:
            Config.VECTOR_STORE_CONFIG['max_features'] = env_config['max_features']
        
        if 'top_k_default' in env_config:
            Config.RAG_CONFIG['top_k_default'] = env_config['top_k_default']
        
        if 'similarity_threshold' in env_config:
            Config.RAG_CONFIG['similarity_threshold'] = env_config['similarity_threshold']
        
        if 'max_concurrent_queries' in env_config:
            Config.PERFORMANCE_CONFIG['max_concurrent_queries'] = env_config['max_concurrent_queries']
        
        if 'query_timeout' in env_config:
            Config.PERFORMANCE_CONFIG['query_timeout'] = env_config['query_timeout']
        
        if 'api_host' in env_config:
            Config.API_CONFIG['host'] = env_config['api_host']
        
        if 'api_port' in env_config:
            Config.API_CONFIG['port'] = env_config['api_port']


if __name__ == "__main__":
    # 测试配置
    print("=== 配置测试 ===\n")
    
    # 创建目录
    Config.create_directories()
    
    # 显示配置
    config = Config.get_config()
    print("系统配置:")
    for section, settings in config.items():
        print(f"\n{section.upper()}:")
        for key, value in settings.items():
            print(f"  {key}: {value}")
    
    # 测试环境变量配置
    print("\n=== 环境变量配置测试 ===")
    os.environ['DEBUG'] = 'false'
    os.environ['EMBEDDING_DIM'] = '300'
    os.environ['TOP_K_DEFAULT'] = '10'
    
    EnvironmentConfig.apply_env_config()
    
    print(f"DEBUG: {Config.DEBUG}")
    print(f"EMBEDDING_DIM: {Config.VECTOR_STORE_CONFIG['embedding_dim']}")
    print(f"TOP_K_DEFAULT: {Config.RAG_CONFIG['top_k_default']}")
    
    print("\n配置测试完成!")
