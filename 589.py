class Node:
    def __init__(self, val=None, children=None):
        self.val = val
        self.children = []

    def add_child(self, node):
        self.children.append(node)


def preorder(root):
    """
    :type root: Node
    :rtype: List[int]
    """
    if root is None:
        return []

    stack, output = [root, ], []
    while stack:
        root = stack.pop()
        output.append(root.val)
        stack.extend(root.children[::-1])

    return output


l = [1, None, 3, 2, 4, None, 5, 6]

root = Node(1)
r3 = Node(3)
r2 = Node(2)
r4 = Node(4)
r3.add_child(Node(5))
r3.add_child(Node(6))
root.add_child(r3)
root.add_child(r2)
root.add_child(r4)

print(preorder(root))

# root = Node(l[0])
for i in l:
    if i is not None:
        exec(f'n_{i} = Node(i)')
        exec(f'root.add_child(n_{i})')

print(preorder(root))




# r = preorder(Node(None, l))
# print(r)
#
# exit()
# rl = l[::]
# print(rl)
# node = Node(rl[0], None)
#
# for i in rl[1:]:
#     node = Node(i, node)
#
# preorder(node)
# for i, v in enumerate(l):
#     next = l[i + 1] if i + 1 > len(l) else None
#     r = preorder(Node(v, next))


s = '''
1 ******
1.1
1.1.1
1.1.2
1.1.2.1
1.2
1.3
2 ****
2.1
2.2
3 ****
3.1
'''


'''
17 [<__main__.Node object at 0x7f79cae73e20>, <__main__.Node object at 0x7f79cae80250>, <__main__.Node object at 0x7f79cae807c0>] 1
20 [<__main__.Node object at 0x7f79cb3860a0>]
22 <__main__.Node object at 0x7f79cb3860a0>
24 1
26 [<__main__.Node object at 0x7f79cae807c0>, <__main__.Node object at 0x7f79cae80250>, <__main__.Node object at 0x7f79cae73e20>]
20 [<__main__.Node object at 0x7f79cae807c0>, <__main__.Node object at 0x7f79cae80250>, <__main__.Node object at 0x7f79cae73e20>]
22 <__main__.Node object at 0x7f79cae73e20>
24 3
26 [<__main__.Node object at 0x7f79cae80b80>, <__main__.Node object at 0x7f79cae808b0>]
20 [<__main__.Node object at 0x7f79cae807c0>, <__main__.Node object at 0x7f79cae80250>, <__main__.Node object at 0x7f79cae80b80>, <__main__.Node object at 0x7f79cae808b0>]
22 <__main__.Node object at 0x7f79cae808b0>
24 5
26 []
20 [<__main__.Node object at 0x7f79cae807c0>, <__main__.Node object at 0x7f79cae80250>, <__main__.Node object at 0x7f79cae80b80>]
22 <__main__.Node object at 0x7f79cae80b80>
24 6
26 []
20 [<__main__.Node object at 0x7f79cae807c0>, <__main__.Node object at 0x7f79cae80250>]
22 <__main__.Node object at 0x7f79cae80250>
24 2
26 []
20 [<__main__.Node object at 0x7f79cae807c0>]
22 <__main__.Node object at 0x7f79cae807c0>
24 4
26 []


'''