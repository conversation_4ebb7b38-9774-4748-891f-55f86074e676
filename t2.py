class Node:
    def __init__(self, val=None, children=None):
        self.val = val
        self.children = []

    def add_child(self, node):
        self.children.append(node)



s = '''
1
1.1
1.1.1
1.1.2
1.1.2.1
1.2
1.2.1
1.2.1.1
1.2.2.2
1.2.2
1.3
2
2.1
2.2
3
3.1
'''

l = []
root = Node(0)


# def f(s):
#     for i in s.split('\n'):
#
#
#
# for i in s.split('\n'):
#     if i:
#         if i[1] == ' ':
#             l.append()


# print(l)