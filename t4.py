s = '''
1
1.1
1.1.1
1.1.2
1.1.2.1
1.2
1.2.1
1.2.1.1
1.2.2.2
1.2.2
1.3
2
2.1
2.2
3
3.1
'''
l = [i.split('.') for i in s.split('\n') if i]
n_l = [i.split('.') for i in s.split('\n') if i][::-1]


class Node(object):
    def __init__(self, val, children=None):
        self.val = val
        self.children = children if children is not None else []

    def add_children(self, *children):
        [self.children.append(child) for child in children]

    def insert_child(self, child):
        self.children.insert(0, child)

def f2(n_l):
    d = {}
    root = Node('0')
    b = 9999999
    for i, v in enumerate(n_l):
        k = len(v)

        if k > b:
            for j in range(k - b):
                d.pop(k - j, None)
        b = k

        if k > 1:
            if k not in d:
                d[k] = [Node(v[-1])]
            else:
                d[k] = [Node(v[-1])] + d[k]
            if k + 1 in d:
                d[k][-1].add_children(*d[k + 1])

        else:
            new_node = Node(v[-1])
            if k + 1 in d:
                new_node.add_children(*d[k + 1])
            root.insert_child(new_node)

    return root


def preorder(root):

    if root is None:
        return []

    stack, output = [root, ], []
    while stack:
        root = stack.pop()
        output.append(root.val)
        stack.extend(root.children[::-1])

    return output

root = f2(n_l)
print(preorder(root))
print(['0'] + [i[-1] for i in n_l])  # 这个应该是正确答案
