"""
法律知识库
包含美国法律文档、案例和相关数据
"""

from typing import List, Dict, Any
from rag_retrieval import Document
import json


class LegalKnowledgeBase:
    """法律知识库"""
    
    def __init__(self):
        self.documents = []
        self._load_knowledge_base()
    
    def _load_knowledge_base(self) -> None:
        """加载法律知识库"""
        # 合同法文档
        contract_law_docs = self._create_contract_law_docs()
        
        # 侵权法文档
        tort_law_docs = self._create_tort_law_docs()
        
        # 宪法文档
        constitutional_law_docs = self._create_constitutional_law_docs()
        
        # 刑法文档
        criminal_law_docs = self._create_criminal_law_docs()
        
        # 财产法文档
        property_law_docs = self._create_property_law_docs()
        
        # 公司法文档
        corporate_law_docs = self._create_corporate_law_docs()
        
        # 家庭法文档
        family_law_docs = self._create_family_law_docs()
        
        # 劳动法文档
        employment_law_docs = self._create_employment_law_docs()
        
        # 合并所有文档
        self.documents.extend(contract_law_docs)
        self.documents.extend(tort_law_docs)
        self.documents.extend(constitutional_law_docs)
        self.documents.extend(criminal_law_docs)
        self.documents.extend(property_law_docs)
        self.documents.extend(corporate_law_docs)
        self.documents.extend(family_law_docs)
        self.documents.extend(employment_law_docs)
    
    def _create_contract_law_docs(self) -> List[Document]:
        """创建合同法文档"""
        return [
            Document(
                id="contract_001",
                title="Elements of a Valid Contract",
                content="""A valid contract requires four essential elements: (1) Offer - a clear proposal to enter into an agreement; (2) Acceptance - an unqualified agreement to the terms of the offer; (3) Consideration - something of value exchanged between the parties; and (4) Mutual assent - both parties must understand and agree to the essential terms. Without all four elements, a contract may be void or voidable. The offer must be definite and certain, and acceptance must mirror the terms of the offer exactly (mirror image rule). Consideration can be a promise, performance, or forbearance, but it must have legal value.""",
                metadata={
                    "document_type": "statute",
                    "domain": "contract_law",
                    "authority": "federal",
                    "jurisdiction": "United States",
                    "date": "2023-01-15",
                    "source": "Restatement of Contracts",
                    "tags": ["contract formation", "elements", "offer", "acceptance", "consideration"]
                }
            ),
            Document(
                id="contract_002",
                title="Breach of Contract and Remedies",
                content="""A breach of contract occurs when one party fails to perform any duty or obligation specified in the contract. Breaches can be material (substantial) or minor (partial). Material breach excuses the non-breaching party from further performance and allows them to seek damages. Remedies for breach include: (1) Compensatory damages - to put the injured party in the position they would have been in if the contract had been performed; (2) Consequential damages - foreseeable losses resulting from the breach; (3) Incidental damages - costs incurred as a result of the breach; (4) Punitive damages - rarely awarded in contract cases; (5) Specific performance - court order requiring actual performance; and (6) Restitution - to prevent unjust enrichment.""",
                metadata={
                    "document_type": "case",
                    "domain": "contract_law",
                    "authority": "state",
                    "jurisdiction": "New York",
                    "date": "2023-02-20",
                    "source": "Hadley v. Baxendale",
                    "tags": ["breach", "damages", "remedies", "specific performance"]
                }
            ),
            Document(
                id="contract_003",
                title="Contract Defenses and Voidability",
                content="""Several defenses can make a contract void or voidable: (1) Lack of capacity - minors, mentally incompetent persons, and intoxicated individuals may lack capacity; (2) Duress - physical or economic coercion that overcomes free will; (3) Undue influence - taking advantage of a relationship of trust; (4) Misrepresentation - false statements of material fact; (5) Mistake - mutual mistake of material fact may void a contract; (6) Illegality - contracts for illegal purposes are void; (7) Unconscionability - extremely unfair terms; and (8) Statute of Frauds - certain contracts must be in writing. Void contracts have no legal effect, while voidable contracts can be affirmed or disaffirmed by the affected party.""",
                metadata={
                    "document_type": "regulation",
                    "domain": "contract_law",
                    "authority": "federal",
                    "jurisdiction": "United States",
                    "date": "2023-03-10",
                    "source": "UCC Article 2",
                    "tags": ["defenses", "capacity", "duress", "misrepresentation", "statute of frauds"]
                }
            )
        ]
    
    def _create_tort_law_docs(self) -> List[Document]:
        """创建侵权法文档"""
        return [
            Document(
                id="tort_001",
                title="Elements of Negligence",
                content="""Negligence is the failure to exercise reasonable care under the circumstances. To establish a negligence claim, the plaintiff must prove four elements: (1) Duty - the defendant owed a legal duty of care to the plaintiff; (2) Breach - the defendant breached that duty by failing to conform to the required standard of care; (3) Causation - the defendant's breach was both the cause-in-fact (but-for causation) and proximate cause of the plaintiff's injury; and (4) Damages - the plaintiff suffered actual harm or injury. The standard of care is typically that of a reasonable person under similar circumstances. Special standards apply to professionals, children, and those with disabilities.""",
                metadata={
                    "document_type": "case",
                    "domain": "tort_law",
                    "authority": "supreme_court",
                    "jurisdiction": "United States",
                    "date": "2023-01-25",
                    "source": "Palsgraf v. Long Island Railroad Co.",
                    "tags": ["negligence", "duty", "breach", "causation", "damages"]
                }
            ),
            Document(
                id="tort_002",
                title="Intentional Torts",
                content="""Intentional torts require the defendant to act with intent to cause the harmful or offensive contact or apprehension thereof. Common intentional torts include: (1) Battery - intentional harmful or offensive contact with another person; (2) Assault - intentional act that creates reasonable apprehension of imminent harmful or offensive contact; (3) False imprisonment - intentional confinement within boundaries fixed by the actor; (4) Intentional infliction of emotional distress - extreme and outrageous conduct that causes severe emotional distress; (5) Trespass to land - intentional entry onto another's property; (6) Trespass to chattels - intentional interference with another's personal property; and (7) Conversion - intentional exercise of dominion over another's property. Intent can be specific (desire to cause the result) or general (knowledge that the result is substantially certain to occur).""",
                metadata={
                    "document_type": "statute",
                    "domain": "tort_law",
                    "authority": "state",
                    "jurisdiction": "California",
                    "date": "2023-02-15",
                    "source": "Restatement of Torts",
                    "tags": ["intentional torts", "battery", "assault", "false imprisonment", "emotional distress"]
                }
            ),
            Document(
                id="tort_003",
                title="Strict Liability",
                content="""Strict liability imposes liability without regard to fault or intent. It applies in three main areas: (1) Abnormally dangerous activities - activities that create a high risk of serious harm and cannot be made safe through reasonable care (e.g., blasting, storing hazardous chemicals); (2) Products liability - manufacturers and sellers are strictly liable for defective products that cause injury; and (3) Keeping of wild animals - owners are strictly liable for harm caused by wild animals. Under strict liability, the plaintiff need only prove that the defendant engaged in the activity and that the activity caused the plaintiff's harm. Defenses to strict liability are limited and typically include assumption of risk, comparative fault (in some jurisdictions), and acts of God or third parties.""",
                metadata={
                    "document_type": "case",
                    "domain": "tort_law",
                    "authority": "federal",
                    "jurisdiction": "United States",
                    "date": "2023-03-05",
                    "source": "Rylands v. Fletcher",
                    "tags": ["strict liability", "abnormally dangerous", "products liability", "wild animals"]
                }
            )
        ]
    
    def _create_constitutional_law_docs(self) -> List[Document]:
        """创建宪法文档"""
        return [
            Document(
                id="const_001",
                title="First Amendment - Freedom of Speech",
                content="""The First Amendment protects freedom of speech from government interference. Speech includes verbal, written, and symbolic expression. The Supreme Court has established different levels of protection: (1) Content-based restrictions receive strict scrutiny and are presumptively invalid unless narrowly tailored to serve a compelling government interest; (2) Content-neutral time, place, and manner restrictions receive intermediate scrutiny; (3) Commercial speech receives intermediate protection; (4) Obscenity, fighting words, true threats, and incitement to imminent lawless action receive no First Amendment protection. The government cannot engage in prior restraint except in extraordinary circumstances. Public forums (traditional, designated, and limited) have different rules for speech restrictions. Private property owners generally have more latitude to restrict speech.""",
                metadata={
                    "document_type": "statute",
                    "domain": "constitutional_law",
                    "authority": "supreme_court",
                    "jurisdiction": "United States",
                    "date": "2023-01-30",
                    "source": "U.S. Constitution Amendment I",
                    "tags": ["first amendment", "free speech", "content-based", "public forum", "prior restraint"]
                }
            ),
            Document(
                id="const_002",
                title="Fourth Amendment - Search and Seizure",
                content="""The Fourth Amendment protects against unreasonable searches and seizures by government officials. A search occurs when the government violates a reasonable expectation of privacy. A seizure occurs when the government meaningfully interferes with a person's possessory interests. Generally, searches and seizures must be supported by a warrant based on probable cause, but numerous exceptions exist: (1) Search incident to arrest; (2) Automobile exception; (3) Plain view doctrine; (4) Consent searches; (5) Exigent circumstances; (6) Administrative searches; (7) Border searches; and (8) Stop and frisk. The exclusionary rule prevents illegally obtained evidence from being used in criminal prosecutions, subject to exceptions like inevitable discovery and independent source.""",
                metadata={
                    "document_type": "case",
                    "domain": "constitutional_law",
                    "authority": "supreme_court",
                    "jurisdiction": "United States",
                    "date": "2023-02-25",
                    "source": "Katz v. United States",
                    "tags": ["fourth amendment", "search", "seizure", "warrant", "probable cause", "exclusionary rule"]
                }
            ),
            Document(
                id="const_003",
                title="Fourteenth Amendment - Equal Protection",
                content="""The Equal Protection Clause of the Fourteenth Amendment requires that similarly situated individuals be treated alike by the government. The Supreme Court applies different levels of scrutiny: (1) Strict scrutiny applies to classifications based on race, national origin, and religion, and to fundamental rights; the law must be narrowly tailored to serve a compelling government interest; (2) Intermediate scrutiny applies to gender and legitimacy classifications; the law must be substantially related to an important government interest; (3) Rational basis review applies to all other classifications; the law must be rationally related to a legitimate government interest. The clause applies only to state action, not private discrimination. Affirmative action programs receive strict scrutiny but may be constitutional if they serve compelling interests like diversity in education.""",
                metadata={
                    "document_type": "statute",
                    "domain": "constitutional_law",
                    "authority": "supreme_court",
                    "jurisdiction": "United States",
                    "date": "2023-03-15",
                    "source": "U.S. Constitution Amendment XIV",
                    "tags": ["fourteenth amendment", "equal protection", "strict scrutiny", "intermediate scrutiny", "rational basis"]
                }
            )
        ]
    
    def _create_criminal_law_docs(self) -> List[Document]:
        """创建刑法文档"""
        return [
            Document(
                id="criminal_001",
                title="Elements of a Crime",
                content="""Most crimes consist of two essential elements: (1) Actus reus - the guilty act or criminal conduct; and (2) Mens rea - the guilty mind or criminal intent. Actus reus requires a voluntary act or omission where there is a legal duty to act. Mens rea has four levels: (1) Purposely - conscious object to cause the result; (2) Knowingly - aware that conduct will cause the result; (3) Recklessly - conscious disregard of substantial and unjustifiable risk; (4) Negligently - should have been aware of substantial and unjustifiable risk. Some crimes require additional elements like causation (result crimes) or attendant circumstances. Strict liability crimes do not require mens rea. The prosecution must prove all elements beyond a reasonable doubt.""",
                metadata={
                    "document_type": "statute",
                    "domain": "criminal_law",
                    "authority": "federal",
                    "jurisdiction": "United States",
                    "date": "2023-01-20",
                    "source": "Model Penal Code",
                    "tags": ["actus reus", "mens rea", "elements", "intent", "strict liability"]
                }
            ),
            Document(
                id="criminal_002",
                title="Criminal Defenses",
                content="""Criminal defendants may raise various defenses: (1) Justification defenses - conduct is justified under the circumstances (self-defense, defense of others, defense of property, necessity, law enforcement); (2) Excuse defenses - defendant is not blameworthy (insanity, duress, intoxication, infancy, mistake); (3) Procedural defenses - violations of constitutional rights (illegal search, Miranda violations, speedy trial violations); (4) Alibi - defendant was elsewhere when crime occurred. Self-defense requires reasonable belief that force is necessary to prevent imminent harm, and the force used must be proportional. Insanity defenses vary by jurisdiction (M'Naghten rule, irresistible impulse, Durham rule, Model Penal Code test). Burden of proof for defenses varies by jurisdiction and type of defense.""",
                metadata={
                    "document_type": "case",
                    "domain": "criminal_law",
                    "authority": "state",
                    "jurisdiction": "Texas",
                    "date": "2023-02-10",
                    "source": "People v. Goetz",
                    "tags": ["defenses", "self-defense", "insanity", "duress", "justification", "excuse"]
                }
            )
        ]
    
    def _create_property_law_docs(self) -> List[Document]:
        """创建财产法文档"""
        return [
            Document(
                id="property_001",
                title="Types of Property Ownership",
                content="""Property law recognizes several types of ownership interests in real property: (1) Fee simple absolute - complete ownership with unlimited duration; (2) Life estate - ownership for the duration of a person's life; (3) Fee simple determinable - ownership that automatically ends upon occurrence of a specified event; (4) Fee simple subject to condition subsequent - ownership that may be terminated by the grantor upon occurrence of a specified condition; (5) Leasehold estates - temporary possessory interests (term of years, periodic tenancy, tenancy at will, tenancy at sufferance). Concurrent ownership includes joint tenancy (with right of survivorship), tenancy in common, and tenancy by the entirety (for married couples). Each type of ownership carries different rights, responsibilities, and transferability rules.""",
                metadata={
                    "document_type": "statute",
                    "domain": "property_law",
                    "authority": "state",
                    "jurisdiction": "California",
                    "date": "2023-01-12",
                    "source": "California Civil Code",
                    "tags": ["fee simple", "life estate", "leasehold", "joint tenancy", "tenancy in common"]
                }
            ),
            Document(
                id="property_002",
                title="Easements and Servitudes",
                content="""An easement is a non-possessory interest in another's land that gives the holder the right to use the land for a specific purpose. Types of easements include: (1) Easement appurtenant - benefits the holder's adjacent land (dominant estate) and burdens the servient estate; (2) Easement in gross - benefits the holder personally, not tied to ownership of land; (3) Affirmative easement - right to do something on another's land; (4) Negative easement - right to prevent the landowner from doing something. Easements can be created by: express grant or reservation, implication (necessity or prior use), prescription (adverse use), or estoppel. Easements generally run with the land and bind successive owners. They can be terminated by abandonment, merger, release, or changed circumstances.""",
                metadata={
                    "document_type": "case",
                    "domain": "property_law",
                    "authority": "state",
                    "jurisdiction": "New York",
                    "date": "2023-02-05",
                    "source": "Willard v. First Church of Christ",
                    "tags": ["easement", "appurtenant", "in gross", "prescription", "implication"]
                }
            )
        ]
    
    def _create_corporate_law_docs(self) -> List[Document]:
        """创建公司法文档"""
        return [
            Document(
                id="corporate_001",
                title="Corporate Formation and Structure",
                content="""A corporation is a legal entity separate from its owners (shareholders). Key characteristics include: (1) Limited liability - shareholders are generally not personally liable for corporate debts; (2) Perpetual existence - corporation continues despite changes in ownership; (3) Centralized management - board of directors manages corporation; (4) Free transferability of shares - ownership interests can be easily transferred. Formation requires filing articles of incorporation with the state, which must include corporate name, purpose, authorized shares, registered agent, and incorporator information. Corporate governance involves three levels: (1) Shareholders - elect directors and approve major transactions; (2) Board of directors - manages corporate affairs and selects officers; (3) Officers - handle day-to-day operations. Bylaws govern internal corporate procedures and can be amended by directors or shareholders.""",
                metadata={
                    "document_type": "statute",
                    "domain": "corporate_law",
                    "authority": "state",
                    "jurisdiction": "Delaware",
                    "date": "2023-01-18",
                    "source": "Delaware General Corporation Law",
                    "tags": ["corporation", "formation", "limited liability", "governance", "shareholders"]
                }
            )
        ]
    
    def _create_family_law_docs(self) -> List[Document]:
        """创建家庭法文档"""
        return [
            Document(
                id="family_001",
                title="Marriage and Divorce",
                content="""Marriage is a legal union between two people that creates various rights and obligations. Requirements for valid marriage include: (1) Legal capacity - parties must be of legal age and mental capacity; (2) Consent - both parties must freely consent; (3) No prohibited relationships - parties cannot be closely related; (4) Compliance with formalities - license, ceremony, and registration. Divorce terminates the marriage relationship. Grounds for divorce vary by state but include: (1) No-fault grounds - irreconcilable differences or irretrievable breakdown; (2) Fault grounds - adultery, cruelty, abandonment, substance abuse. Divorce proceedings address property division, spousal support (alimony), child custody, and child support. Community property states divide marital property equally, while equitable distribution states divide property fairly but not necessarily equally.""",
                metadata={
                    "document_type": "statute",
                    "domain": "family_law",
                    "authority": "state",
                    "jurisdiction": "California",
                    "date": "2023-02-12",
                    "source": "California Family Code",
                    "tags": ["marriage", "divorce", "no-fault", "property division", "alimony"]
                }
            )
        ]
    
    def _create_employment_law_docs(self) -> List[Document]:
        """创建劳动法文档"""
        return [
            Document(
                id="employment_001",
                title="Employment Discrimination Laws",
                content="""Federal employment discrimination laws prohibit discrimination based on protected characteristics: (1) Title VII of the Civil Rights Act of 1964 - prohibits discrimination based on race, color, religion, sex, or national origin; (2) Age Discrimination in Employment Act (ADEA) - protects individuals 40 and older; (3) Americans with Disabilities Act (ADA) - prohibits discrimination against qualified individuals with disabilities; (4) Equal Pay Act - requires equal pay for equal work regardless of sex; (5) Pregnancy Discrimination Act - prohibits discrimination based on pregnancy, childbirth, or related conditions. Discrimination can be: (1) Disparate treatment - intentional discrimination; (2) Disparate impact - neutral policies that disproportionately affect protected groups; (3) Harassment - unwelcome conduct that creates hostile work environment. Employers must provide reasonable accommodations for disabilities and religious practices unless it causes undue hardship.""",
                metadata={
                    "document_type": "statute",
                    "domain": "employment_law",
                    "authority": "federal",
                    "jurisdiction": "United States",
                    "date": "2023-01-28",
                    "source": "Title VII Civil Rights Act",
                    "tags": ["discrimination", "title vii", "ada", "adea", "harassment", "accommodation"]
                }
            )
        ]
    
    def get_all_documents(self) -> List[Document]:
        """获取所有文档"""
        return self.documents
    
    def get_documents_by_domain(self, domain: str) -> List[Document]:
        """按领域获取文档"""
        return [doc for doc in self.documents if doc.metadata.get('domain') == domain]
    
    def get_documents_by_type(self, doc_type: str) -> List[Document]:
        """按类型获取文档"""
        return [doc for doc in self.documents if doc.metadata.get('document_type') == doc_type]
    
    def search_by_tags(self, tags: List[str]) -> List[Document]:
        """按标签搜索文档"""
        results = []
        for doc in self.documents:
            doc_tags = doc.metadata.get('tags', [])
            if any(tag in doc_tags for tag in tags):
                results.append(doc)
        return results
    
    def get_knowledge_base_stats(self) -> Dict[str, Any]:
        """获取知识库统计信息"""
        stats = {
            'total_documents': len(self.documents),
            'domains': {},
            'document_types': {},
            'authorities': {},
            'jurisdictions': {}
        }
        
        for doc in self.documents:
            # 统计领域
            domain = doc.metadata.get('domain', 'unknown')
            stats['domains'][domain] = stats['domains'].get(domain, 0) + 1
            
            # 统计文档类型
            doc_type = doc.metadata.get('document_type', 'unknown')
            stats['document_types'][doc_type] = stats['document_types'].get(doc_type, 0) + 1
            
            # 统计权威性
            authority = doc.metadata.get('authority', 'unknown')
            stats['authorities'][authority] = stats['authorities'].get(authority, 0) + 1
            
            # 统计管辖区
            jurisdiction = doc.metadata.get('jurisdiction', 'unknown')
            stats['jurisdictions'][jurisdiction] = stats['jurisdictions'].get(jurisdiction, 0) + 1
        
        return stats


if __name__ == "__main__":
    # 测试法律知识库
    print("=== 法律知识库测试 ===\n")
    
    kb = LegalKnowledgeBase()
    
    # 显示统计信息
    stats = kb.get_knowledge_base_stats()
    print("知识库统计:")
    print(f"  总文档数: {stats['total_documents']}")
    print(f"  领域分布: {stats['domains']}")
    print(f"  文档类型: {stats['document_types']}")
    print(f"  权威级别: {stats['authorities']}")
    print()
    
    # 测试按领域搜索
    contract_docs = kb.get_documents_by_domain('contract_law')
    print(f"合同法文档数量: {len(contract_docs)}")
    for doc in contract_docs:
        print(f"  - {doc.title}")
    print()
    
    # 测试按标签搜索
    negligence_docs = kb.search_by_tags(['negligence', 'duty'])
    print(f"包含'negligence'或'duty'标签的文档:")
    for doc in negligence_docs:
        print(f"  - {doc.title} (标签: {doc.metadata.get('tags', [])})")
    print()
    
    # 显示示例文档内容
    sample_doc = kb.documents[0]
    print(f"示例文档: {sample_doc.title}")
    print(f"内容预览: {sample_doc.content[:200]}...")
    print(f"元数据: {json.dumps(sample_doc.metadata, indent=2)}")
