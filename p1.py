import datetime
import json

j = '''{"code":0,"message":"0","ttl":1,"data":{"attr_map":{"5":{"name":"今日小贴士","icon":"http://i0.hdslb.com/bfs/creative/660158f99c15d3d601e71a9accaf70cb05523c8e.png","rank":1},"6":{"name":"创作纪念日","icon":"http://i0.hdslb.com/bfs/creative/bdbc1021e6dcdcb8b28f069acf9151d19a42c65a.png","rank":2},"7":{"name":"热点事件","icon":"http://i0.hdslb.com/bfs/creative/2da892887c0add97a9d493ac0f958275f943ba04.png","rank":3}},"calendar_attrs":{},"events":{},"flag_completion":{},"pfs":{"profile":{"mid":*********,"name":"取名字真的好烦啊啊啊","sex":"保密","face":"http://i0.hdslb.com/bfs/face/member/noface.jpg","sign":"","rank":10000,"level":6,"jointime":1516504848,"moral":70,"silence":0,"email_status":0,"tel_status":1,"identification":0,"vip":{"type":2,"status":1,"due_date":1787068800000,"vip_pay_type":1,"theme_type":0,"label":{"path":"","text":"年度大会员","label_theme":"annual_vip","text_color":"#FFFFFF","bg_style":1,"bg_color":"#FB7299","border_color":"","use_img_label":true,"img_label_uri_hans":"","img_label_uri_hant":"","img_label_uri_hans_static":"https://i0.hdslb.com/bfs/vip/8d4f8bfc713826a5412a0a27eaaac4d6b9ede1d9.png","img_label_uri_hant_static":"https://i0.hdslb.com/bfs/activity-plat/static/20220614/e369244d0b14644f5e1a06431e22a4d5/VEW8fCC0hg.png"},"avatar_subscript":1,"nickname_color":"#FB7299","role":3,"avatar_subscript_url":"","tv_vip_status":0,"tv_vip_pay_type":0,"tv_due_date":0,"avatar_icon":{"icon_type":1,"icon_resource":{}}},"pendant":{"pid":0,"name":"","image":"","expire":0,"image_enhance":"","image_enhance_frame":"","n_pid":0},"nameplate":{"nid":0,"name":"","image":"","image_small":"","level":"","condition":""},"official":{"role":0,"title":"","desc":"","type":-1},"birthday":*********,"is_tourist":0,"is_fake_account":0,"pin_prompting":0,"is_deleted":0,"in_reg_audit":0,"is_rip_user":false,"profession":{"id":0,"name":"","show_name":"","is_show":0,"category_one":"","realname":"","title":"","department":"","certificate_no":"","certificate_show":false},"face_nft":0,"face_nft_new":0,"is_senior_member":0,"honours":{"mid":*********,"colour":{"dark":"#CE8620","normal":"#F0900B"},"tags":null,"is_latest_100honour":0},"digital_id":"","digital_type":-2,"attestation":{"type":0,"common_info":{"title":"","prefix":"","prefix_title":""},"splice_info":{"title":""},"icon":"","desc":""},"expert_info":{"title":"","state":0,"type":0,"desc":""},"name_render":null,"country_code":"86"},"level_info":{"current_level":6,"current_min":28800,"current_exp":29125,"next_exp":-1,"level_up":**********},"coins":1880,"following":1105,"follower":3,"masked_privacy":{"mid":0,"realname":"","identity_card":"","telephone":"","login_ip":"","email":""},"user_honour_info":{"mid":*********,"colour":{"dark":"#CE8620","normal":"#F0900B"},"tags":null,"is_latest_100honour":0},"school":{"school_id":422,"name":"沈阳化工大学"}}}}'''
d = json.loads(j)

print(json.dumps(d, indent=4, ensure_ascii=False))

dt = datetime.datetime.fromtimestamp(1516504848)
print(dt)